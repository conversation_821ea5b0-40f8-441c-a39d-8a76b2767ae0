const _0x5912c6=_0x2a7b;(function(_0x21605e,_0x173f9e){const _0x448669=_0x2a7b,_0x43afb2=_0x21605e();while(!![]){try{const _0x4020e2=-parseInt(_0x448669(0x145))/0x1*(-parseInt(_0x448669(0x162))/0x2)+parseInt(_0x448669(0x1c3))/0x3*(-parseInt(_0x448669(0x16e))/0x4)+parseInt(_0x448669(0x174))/0x5+parseInt(_0x448669(0x185))/0x6+parseInt(_0x448669(0x1b5))/0x7+-parseInt(_0x448669(0x1d8))/0x8+-parseInt(_0x448669(0x1d3))/0x9*(parseInt(_0x448669(0x149))/0xa);if(_0x4020e2===_0x173f9e)break;else _0x43afb2['push'](_0x43afb2['shift']());}catch(_0x9701f6){_0x43afb2['push'](_0x43afb2['shift']());}}}(_0x4c61,0x7c42f));let currentProjectIndex=null,currentImageIndex=null,currentProjectImages=[],isLightboxTransitioning=!0x1,isNavigationThrottled=!0x1;function _0x2a7b(_0x17460b,_0x4a397b){const _0x4c61f3=_0x4c61();return _0x2a7b=function(_0x2a7b49,_0x203acc){_0x2a7b49=_0x2a7b49-0x136;let _0xd8e77b=_0x4c61f3[_0x2a7b49];if(_0x2a7b['zUyrar']===undefined){var _0x1d9982=function(_0x42bf65){const _0x198e48='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';let _0x62bd83='',_0x271f7e='';for(let _0x4c3b60=0x0,_0x1bd579,_0x4ff9e6,_0xcfe746=0x0;_0x4ff9e6=_0x42bf65['charAt'](_0xcfe746++);~_0x4ff9e6&&(_0x1bd579=_0x4c3b60%0x4?_0x1bd579*0x40+_0x4ff9e6:_0x4ff9e6,_0x4c3b60++%0x4)?_0x62bd83+=String['fromCharCode'](0xff&_0x1bd579>>(-0x2*_0x4c3b60&0x6)):0x0){_0x4ff9e6=_0x198e48['indexOf'](_0x4ff9e6);}for(let _0x40a78c=0x0,_0x368b5d=_0x62bd83['length'];_0x40a78c<_0x368b5d;_0x40a78c++){_0x271f7e+='%'+('00'+_0x62bd83['charCodeAt'](_0x40a78c)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x271f7e);};_0x2a7b['JYKdyS']=_0x1d9982,_0x17460b=arguments,_0x2a7b['zUyrar']=!![];}const _0x2e18dc=_0x4c61f3[0x0],_0x2b9532=_0x2a7b49+_0x2e18dc,_0x3c183c=_0x17460b[_0x2b9532];return!_0x3c183c?(_0xd8e77b=_0x2a7b['JYKdyS'](_0xd8e77b),_0x17460b[_0x2b9532]=_0xd8e77b):_0xd8e77b=_0x3c183c,_0xd8e77b;},_0x2a7b(_0x17460b,_0x4a397b);}const NAVIGATION_THROTTLE_DURATION=0x12c;let closeLightboxTimeoutId=null,onLightboxCloseTransitionEnd=null,lightboxOpenGeneration=0x0;function isDesktop(){const _0x5adf38=_0x2a7b;return window[_0x5adf38(0x1f1)](_0x5adf38(0x1e9))[_0x5adf38(0x204)];}function isTouchDevice(){const _0x1b40eb=_0x2a7b;return _0x1b40eb(0x1bc)in window||navigator['maxTouchPoints']>0x0||navigator[_0x1b40eb(0x1d9)]>0x0;}function createEl(_0x42bf65,_0x198e48,_0x62bd83){const _0x3ba964=_0x2a7b,_0x271f7e=document['createElement'](_0x42bf65);return _0x198e48&&(_0x271f7e[_0x3ba964(0x156)]=_0x198e48),void 0x0!==_0x62bd83&&(_0x271f7e[_0x3ba964(0x16b)]=_0x62bd83),_0x271f7e;}function clearChildren(_0x4c3b60){for(;_0x4c3b60['firstChild'];)_0x4c3b60['removeChild'](_0x4c3b60['firstChild']);}function toAbsoluteAssetPath(_0x1bd579){const _0x36a763=_0x2a7b;return _0x1bd579?_0x1bd579['startsWith'](_0x36a763(0x1ff))||_0x1bd579[_0x36a763(0x1b3)](_0x36a763(0x171))||_0x1bd579[_0x36a763(0x1b3)]('../')?_0x1bd579:_0x36a763(0x15b)+_0x1bd579:_0x1bd579;}function sendMessageToParent(_0x4ff9e6){const _0x36f060=_0x2a7b;window['parent']&&window[_0x36f060(0x1e8)]!==window&&window['parent']['postMessage'](_0x4ff9e6,'*');}let lightboxEl=null,lightboxContentEl=null,lightboxDetailsEl=null,allProjectPostsData=[],userPrefersDescriptionVisible=!0x1,userPrefersMuted=!0x0;const MIN_SWIPE_DISTANCE=0x2c;function projectUsesMobileAsset(_0xcfe746){const _0x4b6b23=_0x2a7b,_0x40a78c=currentProjectImages[currentImageIndex];return!(!_0x40a78c||!_0x40a78c['posterMobile'])||!!_0xcfe746[_0x4b6b23(0x19f)];}function createDesktopDescriptionCard(_0x368b5d,_0xd834c8,_0x178222,_0x1fefee,_0x1ba597,_0x54bb48){const _0x15cee5=_0x2a7b,_0x3ee07a=createEl('div',_0x15cee5(0x1ad));clearChildren(_0x3ee07a);const _0x5cde52=createEl(_0x15cee5(0x1dd),'card-main-content');let _0x17284b=_0x15cee5(0x153);'client'===(allProjectPostsData[currentProjectIndex]||{})[_0x15cee5(0x164)]&&(_0x17284b=_0x15cee5(0x1aa));const _0x241d47=createEl(_0x15cee5(0x1dd),_0x15cee5(0x180),_0x17284b);if(_0x5cde52[_0x15cee5(0x178)](_0x241d47),_0x368b5d){const _0x13cd3a=createEl(_0x15cee5(0x1dd),_0x15cee5(0x1be)),_0x357d6d=createEl(_0x15cee5(0x1dd),_0x15cee5(0x161),_0x368b5d);if(_0x13cd3a['appendChild'](_0x357d6d),_0x1ba597>0x1){const _0x1fd83d=createEl('span',_0x15cee5(0x16f));for(let _0x45096d=0x0;_0x45096d<_0x1ba597;_0x45096d++){const _0x22507d=createEl('span',_0x15cee5(0x1e2));_0x22507d[_0x15cee5(0x1c8)]['index']=_0x45096d,_0x22507d[_0x15cee5(0x206)](_0x15cee5(0x176),'button'),_0x22507d[_0x15cee5(0x206)](_0x15cee5(0x15f),'0'),_0x22507d[_0x15cee5(0x206)](_0x15cee5(0x144),'Go\x20to\x20image\x20'+(_0x45096d+0x1)),_0x45096d===_0x54bb48&&_0x22507d['classList'][_0x15cee5(0x170)]('active');const _0x38193b=_0x33e102=>{const _0xf901f=_0x15cee5;_0x33e102[_0xf901f(0x1d5)]();const _0x28ab43=parseInt(_0x33e102[_0xf901f(0x152)]['dataset'][_0xf901f(0x1da)],0xa);_0x28ab43!==currentImageIndex&&(isLightboxTransitioning||isNavigationThrottled||_openLightboxByProjectAndImage(currentProjectIndex,_0x28ab43,0x0,!0x1));};_0x22507d['addEventListener']('click',_0x38193b),_0x22507d['addEventListener'](_0x15cee5(0x1ce),_0x20537e=>{const _0x1ca065=_0x15cee5;_0x1ca065(0x1d2)!==_0x20537e[_0x1ca065(0x172)]&&'\x20'!==_0x20537e['key']||(_0x20537e['preventDefault'](),_0x38193b(_0x20537e));}),_0x1fd83d[_0x15cee5(0x178)](_0x22507d);}_0x13cd3a['appendChild'](_0x1fd83d);}_0x5cde52['appendChild'](_0x13cd3a);const _0x29d8e0=createEl('hr','card-title-divider');_0x5cde52['appendChild'](_0x29d8e0);}const _0x18e7c0=createEl(_0x15cee5(0x1dd),_0x15cee5(0x1c2),_0x15cee5(0x1ef));if(_0x5cde52['appendChild'](_0x18e7c0),_0xd834c8){const _0x49ace3=createEl('div','card-body',_0xd834c8);_0x5cde52[_0x15cee5(0x178)](_0x49ace3);}if(_0x178222&&''!==_0x178222[_0x15cee5(0x140)]()){const _0x562420=createEl('div','card-bullet-points'),_0x260899=createEl('ul');_0x178222[_0x15cee5(0x1c0)]('|')['forEach'](_0x5e6769=>{const _0x59c96f=_0x15cee5;if(_0x5e6769['trim']()){const _0x347dbc=createEl('li',null,_0x5e6769['trim']());_0x260899[_0x59c96f(0x178)](_0x347dbc);}}),_0x562420[_0x15cee5(0x178)](_0x260899),_0x5cde52[_0x15cee5(0x178)](_0x562420);}if(_0x1fefee&&''!==_0x1fefee[_0x15cee5(0x140)]()){const _0x1c93b6=createEl('hr',_0x15cee5(0x1fc));_0x5cde52[_0x15cee5(0x178)](_0x1c93b6);const _0x11983b=createEl(_0x15cee5(0x1dd),_0x15cee5(0x1e4),_0x15cee5(0x196));_0x5cde52[_0x15cee5(0x178)](_0x11983b);const _0x120576=createEl(_0x15cee5(0x1dd),'card-tools-list'),_0x36a694=_0x1fefee[_0x15cee5(0x1c0)](',')[_0x15cee5(0x1cb)](_0x4c07f7=>{const _0x26a283=_0x15cee5,_0x23094f=_0x4c07f7[_0x26a283(0x140)]();return _0x23094f[_0x26a283(0x1a6)]>0x0?_0x23094f[_0x26a283(0x199)](0x0)['toUpperCase']()+_0x23094f['slice'](0x1):_0x23094f;});_0x120576['textContent']=_0x36a694[_0x15cee5(0x1db)](',\x20'),_0x5cde52[_0x15cee5(0x178)](_0x120576);}return _0x3ee07a['appendChild'](_0x5cde52),_0x3ee07a;}function createSpinnerOverlay(){const _0xc610d0=_0x2a7b,_0x9b4c8f=document[_0xc610d0(0x1ab)](_0xc610d0(0x1dd));return _0x9b4c8f[_0xc610d0(0x156)]=_0xc610d0(0x1cc),_0x9b4c8f['innerHTML']=_0xc610d0(0x194),_0x9b4c8f;}function createMuteIconOverlay(_0x596ea1){const _0x4821ac=_0x2a7b,_0x23cf2a=allProjectPostsData[currentProjectIndex]||{},_0x5a0c2b=!isDesktop()&&projectUsesMobileAsset(_0x23cf2a),_0x309f92=_0x596ea1?_0x5a0c2b?_0x4821ac(0x1b6):'voldown':_0x5a0c2b?'volupmob':'volup',_0x1d187e=document[_0x4821ac(0x1ab)](_0x4821ac(0x1dd));return _0x1d187e['className']=_0x4821ac(0x1f2),_0x1d187e['setAttribute'](_0x4821ac(0x1e6),'true'),_0x1d187e[_0x4821ac(0x1fe)]=_0x4821ac(0x19d)+_0x309f92+'.webp\x22\x20alt=\x22'+(_0x596ea1?'Muted':_0x4821ac(0x150))+'\x22\x20draggable=\x22false\x22\x20style=\x22width:100%;height:100%;object-fit:contain;\x22\x20/>',_0x1d187e;}function showMuteIconOverlay(_0xf2a99,_0x4c815f){const _0x20534a=_0x2a7b,_0x4aea70=_0xf2a99[_0x20534a(0x1ec)][_0x20534a(0x13f)](_0x20534a(0x1b1));if(_0x4aea70&&_0x4aea70['remove'](),_0xf2a99['readyState']<0x3&&_0x4c815f)return;const _0x14d2c0=createMuteIconOverlay(_0x4c815f);if(_0xf2a99[_0x20534a(0x1ec)][_0x20534a(0x178)](_0x14d2c0),_0x14d2c0['offsetWidth'],_0x14d2c0['classList']['add'](_0x20534a(0x15e)),_0xf2a99[_0x20534a(0x1d0)]>=0x3&&!_0xf2a99[_0x20534a(0x1a4)])setTimeout(()=>{const _0x111bf5=_0x20534a;_0x14d2c0[_0x111bf5(0x18e)]&&(_0x14d2c0['classList'][_0x111bf5(0x159)](_0x111bf5(0x15e)),setTimeout(()=>{const _0x4c1c4f=_0x111bf5;_0x14d2c0['parentNode']&&_0x14d2c0[_0x4c1c4f(0x159)]();},0x5dc));},0x5dc);else{const _0x43d380=()=>{const _0x58ec11=_0x20534a;_0x14d2c0['parentNode']&&(_0x14d2c0['offsetWidth'],_0x14d2c0['classList'][_0x58ec11(0x170)]('show'),setTimeout(()=>{const _0x5f3152=_0x58ec11;_0x14d2c0['parentNode']&&(_0x14d2c0['classList'][_0x5f3152(0x159)](_0x5f3152(0x15e)),setTimeout(()=>{const _0x52475a=_0x5f3152;_0x14d2c0['parentNode']&&_0x14d2c0[_0x52475a(0x159)]();},0x5dc));},0x5dc)),_0xf2a99[_0x58ec11(0x14e)]('playing',_0x43d380);};_0xf2a99[_0x20534a(0x166)](_0x20534a(0x155),_0x43d380);}}function createLightboxMediaElement(_0x2c1949,_0x3c0c79,_0xf7e5b=null){const _0x4b13c8=_0x2a7b;if(_0x4b13c8(0x1b7)===_0x2c1949){const _0x391cee=createEl('img');return _0x391cee['alt']='Project\x20Lightbox\x20Image',_0x391cee[_0x4b13c8(0x1fd)]=_0x3c0c79,_0x391cee[_0x4b13c8(0x166)]('click',_0x1c4d9e=>{_0x1c4d9e['stopPropagation']();}),_0x391cee;}if(_0x4b13c8(0x1f9)===_0x2c1949){const _0x56f426=createEl('video');_0x56f426['alt']=_0x4b13c8(0x165),_0x56f426['controls']=!0x1,_0x56f426[_0x4b13c8(0x1a1)]=!0x0,_0x56f426['loop']=!0x0,_0x56f426[_0x4b13c8(0x206)]('playsinline',''),_0x56f426[_0x4b13c8(0x206)](_0x4b13c8(0x139),''),isDesktop()?(_0x56f426[_0x4b13c8(0x13d)]=userPrefersMuted,userPrefersMuted?_0x56f426['setAttribute'](_0x4b13c8(0x13d),''):_0x56f426['removeAttribute']('muted')):(_0x56f426[_0x4b13c8(0x13d)]=!0x0,_0x56f426[_0x4b13c8(0x206)](_0x4b13c8(0x13d),'')),_0x56f426['src']=_0x3c0c79,_0xf7e5b&&(_0x56f426[_0x4b13c8(0x175)]=_0xf7e5b);const _0x415bac=document[_0x4b13c8(0x1ab)]('div');_0x415bac['className']='video-outer-wrapper',_0x415bac[_0x4b13c8(0x18a)][_0x4b13c8(0x18d)]='relative',_0x415bac[_0x4b13c8(0x18a)][_0x4b13c8(0x17b)]=_0x4b13c8(0x1fa),_0x415bac[_0x4b13c8(0x18a)][_0x4b13c8(0x15c)]='0',_0x415bac[_0x4b13c8(0x18a)][_0x4b13c8(0x17e)]='0',_0x415bac['style'][_0x4b13c8(0x1af)]='100%',_0x415bac[_0x4b13c8(0x18a)][_0x4b13c8(0x1e5)]='100%',_0x415bac['style'][_0x4b13c8(0x1b9)]='middle',_0x415bac[_0x4b13c8(0x178)](_0x56f426);const _0x3a04bd=createSpinnerOverlay();_0x415bac[_0x4b13c8(0x178)](_0x3a04bd);let _0x4f7d1f=!0x1;function _0x412a2d(){const _0x261404=_0x4b13c8;_0x3a04bd['parentNode']&&_0x3a04bd[_0x261404(0x18e)][_0x261404(0x1d1)](_0x3a04bd);}if(_0x56f426['addEventListener']('playing',()=>{_0x4f7d1f=!0x0,_0x412a2d(),showMuteIconOverlay(_0x56f426,_0x56f426['muted']);}),setTimeout(()=>{_0x4f7d1f||_0x412a2d();},0x1f40),_0x56f426[_0x4b13c8(0x166)](_0x4b13c8(0x1d7),_0x334b1c=>{const _0x21b20c=_0x4b13c8;_0x334b1c['stopPropagation'](),_0x56f426['muted']=!_0x56f426['muted'],_0x56f426['muted']?_0x56f426['setAttribute'](_0x21b20c(0x13d),''):_0x56f426['removeAttribute']('muted'),isDesktop()&&(userPrefersMuted=_0x56f426[_0x21b20c(0x13d)],window[_0x21b20c(0x192)]&&sessionStorage['setItem']('projectsUserPrefersMuted',userPrefersMuted)),_0x4f7d1f&&showMuteIconOverlay(_0x56f426,_0x56f426['muted']);}),!isTouchDevice()){let _0x2bf86b=null;_0x415bac[_0x4b13c8(0x166)](_0x4b13c8(0x1e0),()=>{const _0x25ba63=_0x4b13c8;_0x2bf86b&&clearTimeout(_0x2bf86b),showMuteIconOverlay(_0x56f426,_0x56f426[_0x25ba63(0x13d)]);}),_0x415bac[_0x4b13c8(0x166)](_0x4b13c8(0x1c7),()=>{_0x2bf86b&&clearTimeout(_0x2bf86b),_0x2bf86b=setTimeout(()=>{const _0x4c1d36=_0x2a7b,_0x55944d=_0x415bac[_0x4c1d36(0x13f)]('.mute-icon-overlay');_0x55944d&&(_0x55944d[_0x4c1d36(0x1cf)]['remove']('show'),setTimeout(()=>{const _0x49c209=_0x4c1d36;_0x55944d[_0x49c209(0x18e)]&&_0x55944d['remove']();},0xc8));},0x7d0);}),_0x56f426[_0x4b13c8(0x166)]('volumechange',()=>{const _0x51053e=_0x4b13c8;_0x415bac['matches'](_0x51053e(0x136))&&showMuteIconOverlay(_0x56f426,_0x56f426[_0x51053e(0x13d)]);});}return _0x415bac;}return null;}function updateLightboxDetails(_0x7cb59b,_0x5c3502){const _0x2fddb1=_0x2a7b;if(lightboxDetailsEl&&(clearChildren(lightboxDetailsEl),_0x7cb59b&&(lightboxDetailsEl['appendChild'](createEl(_0x2fddb1(0x1dd),null,_0x7cb59b))['id']='lightbox-title'),_0x5c3502&&!isDesktop())){const _0x5366ce=createEl(_0x2fddb1(0x1dd),null,_0x5c3502);_0x5366ce['id']='lightbox-description',lightboxDetailsEl[_0x2fddb1(0x178)](_0x5366ce);}}function getOrCreateWrapper(){const _0x46d286=_0x2a7b;if(!lightboxContentEl)return null;let _0x499073=lightboxContentEl['querySelector'](_0x46d286(0x137));return _0x499073||(_0x499073=document[_0x46d286(0x1ab)]('div'),_0x499073['className']='lightbox-media-wrapper',lightboxContentEl['appendChild'](_0x499073)),_0x499073;}function updateToolbarNavState(){const _0x43c694=_0x2a7b,_0x540f42=currentProjectImages&&currentProjectImages['length']>0x1,_0x21e66f=currentProjectImages&&currentProjectImages['length']>0x1;let _0x455908=!0x1;if(isDesktop()){const _0x1b163e=lightboxContentEl?lightboxContentEl['querySelector'](_0x43c694(0x137)):null,_0x16bedc=_0x1b163e?_0x1b163e['querySelector'](_0x43c694(0x17c)):null;_0x16bedc&&(_0x455908=userPrefersDescriptionVisible&&!_0x16bedc['classList']['contains']('fully-hidden'));}else _0x455908=userPrefersDescriptionVisible;sendMessageToParent({'type':_0x43c694(0x1a8),'open':!0x0,'hasNext':_0x540f42,'hasPrevious':_0x21e66f,'descriptionState':_0x455908,'shouldDisplayDescriptionToggleButton':!isDesktop()}),'function'==typeof window[_0x43c694(0x16a)]&&window['handleLightboxStateChange'](!0x0);}function _openLightboxByProjectAndImage(_0x8edba9,_0x4d7470,_0x429f80=0x0,_0x30775b=!0x1){const _0x5611fb=_0x2a7b;if(!lightboxEl||!lightboxContentEl||!allProjectPostsData[_0x5611fb(0x1a6)])return;if(isLightboxTransitioning&&0x0!==_0x429f80)return void console[_0x5611fb(0x19a)]('Lightbox\x20transition\x20already\x20in\x20progress,\x20navigation\x20aborted.');isLightboxTransitioning=!0x0,_0x8edba9<0x0&&(_0x8edba9=allProjectPostsData['length']-0x1),_0x8edba9>=allProjectPostsData[_0x5611fb(0x1a6)]&&(_0x8edba9=0x0);const _0x1f075e=allProjectPostsData[_0x8edba9];if(!_0x1f075e)return console[_0x5611fb(0x15d)]('Lightbox:\x20Post\x20data\x20not\x20found\x20for\x20project\x20index:',_0x8edba9),void(isLightboxTransitioning=!0x1);try{let _0x153913=JSON['parse'](_0x1f075e['images']||'[]');Array[_0x5611fb(0x1ae)](_0x153913)&&0x0!==_0x153913[_0x5611fb(0x1a6)]?(currentProjectImages=_0x153913['map'](_0x427799=>'string'==typeof _0x427799?{'type':'image','src':toAbsoluteAssetPath(_0x427799)}:'object'==typeof _0x427799&&_0x427799['src']?{'type':_0x427799['type']||_0x5611fb(0x1b7),'src':toAbsoluteAssetPath(_0x427799['src']),'poster':_0x427799[_0x5611fb(0x175)]?toAbsoluteAssetPath(_0x427799['poster']):null,'posterMobile':_0x427799[_0x5611fb(0x154)]?toAbsoluteAssetPath(_0x427799[_0x5611fb(0x154)]):null}:(console['warn']('Lightbox:\x20Invalid\x20item\x20in\x20images\x20array,\x20skipping:',_0x427799),null))[_0x5611fb(0x1ba)](_0x570b7e=>null!==_0x570b7e),0x0===currentProjectImages['length']&&(currentProjectImages=[{'type':_0x1f075e['type']||'image','src':toAbsoluteAssetPath(_0x1f075e[_0x5611fb(0x1fd)]),'poster':_0x1f075e[_0x5611fb(0x175)]?toAbsoluteAssetPath(_0x1f075e['poster']):null,'posterMobile':_0x1f075e[_0x5611fb(0x154)]?toAbsoluteAssetPath(_0x1f075e[_0x5611fb(0x154)]):null}])):currentProjectImages=[{'type':_0x1f075e[_0x5611fb(0x205)]||'image','src':toAbsoluteAssetPath(_0x1f075e['src']),'poster':_0x1f075e[_0x5611fb(0x175)]?toAbsoluteAssetPath(_0x1f075e['poster']):null}];}catch(_0x146ca9){console[_0x5611fb(0x15d)](_0x5611fb(0x18f),_0x146ca9),currentProjectImages=[{'type':_0x1f075e['type']||'image','src':toAbsoluteAssetPath(_0x1f075e[_0x5611fb(0x1fd)]),'poster':_0x1f075e[_0x5611fb(0x175)]?toAbsoluteAssetPath(_0x1f075e[_0x5611fb(0x175)]):null}];}if(-0x1===_0x4d7470&&(_0x4d7470=currentProjectImages[_0x5611fb(0x1a6)]-0x1),0x0===currentProjectImages[_0x5611fb(0x1a6)])return console[_0x5611fb(0x15d)]('Lightbox:\x20No\x20valid\x20images\x20to\x20display\x20for\x20project\x20index:',_0x8edba9),void(isLightboxTransitioning=!0x1);_0x4d7470>=currentProjectImages[_0x5611fb(0x1a6)]&&(_0x4d7470=Math[_0x5611fb(0x1ac)](0x0,currentProjectImages[_0x5611fb(0x1a6)]-0x1)),_0x4d7470<0x0&&(_0x4d7470=0x0);const _0x4c01a5=currentProjectIndex;currentProjectIndex=_0x8edba9,currentImageIndex=_0x4d7470;const _0x501d4f=currentProjectImages[currentImageIndex];if(!_0x501d4f||!_0x501d4f['src'])return console[_0x5611fb(0x15d)]('Lightbox:\x20Current\x20media\x20item\x20is\x20invalid\x20or\x20missing\x20src\x20at\x20imageIndex:',currentImageIndex,_0x501d4f),void(isLightboxTransitioning=!0x1);[(currentImageIndex+0x1)%currentProjectImages['length'],(currentImageIndex-0x1+currentProjectImages['length'])%currentProjectImages[_0x5611fb(0x1a6)]]['forEach'](_0x4e3a79=>{const _0x351725=_0x5611fb,_0x110341=currentProjectImages[_0x4e3a79];if(_0x351725(0x1b7)===_0x110341[_0x351725(0x205)])new window[(_0x351725(0x17d))]()['src']=_0x110341['src'];else{if(_0x351725(0x1f9)===_0x110341['type']){const _0x2a4df4=document[_0x351725(0x1ab)](_0x351725(0x1f9));_0x2a4df4[_0x351725(0x1d6)]='metadata',_0x2a4df4['src']=_0x110341[_0x351725(0x1fd)];}}});const _0x130e15=_0x501d4f['type'],_0x30ffb4=_0x501d4f[_0x5611fb(0x1fd)],_0x446b1b=!isDesktop()&&_0x501d4f['posterMobile']?_0x501d4f[_0x5611fb(0x154)]:_0x501d4f[_0x5611fb(0x175)],_0xfe0112=_0x1f075e[_0x5611fb(0x14b)],_0x3b6412=_0x1f075e[_0x5611fb(0x17a)],_0x328230=_0x1f075e['mobileDescription'],_0x3c9f33=getOrCreateWrapper(),_0x513255=_0x3c9f33[_0x5611fb(0x13f)]('img,\x20.video-outer-wrapper'),_0x1d800f=_0x3c9f33['querySelector']('.lightbox-desc-card'),_0x28c02d=null===_0x4c01a5||_0x4c01a5!==currentProjectIndex,_0x1b6b57=createLightboxMediaElement(_0x130e15,_0x30ffb4,_0x446b1b);_0x1b6b57[_0x5611fb(0x18a)][_0x5611fb(0x177)]='0';let _0x1e562=Promise[_0x5611fb(0x181)]();_0x513255&&(_0x1e562=new Promise(_0x444c98=>{const _0x4d72b3=_0x5611fb;if(_0x30775b&&!isDesktop()&&0x0!==_0x429f80){const _0x3c8456=0x1===_0x429f80?'-100vw':_0x4d72b3(0x1f4);_0x513255[_0x4d72b3(0x18a)][_0x4d72b3(0x1ed)]=_0x4d72b3(0x1a3),_0x513255['style'][_0x4d72b3(0x1f7)]='translateX('+_0x3c8456+')';const _0x500e90=()=>{const _0x50a2a7=_0x4d72b3;_0x513255[_0x50a2a7(0x14e)]('transitionend',_0x500e90),_0x444c98();};_0x513255['addEventListener']('transitionend',_0x500e90,{'once':!0x0}),setTimeout(_0x500e90,0x104);}else{_0x513255['style']['transition']=_0x4d72b3(0x169),_0x513255['style']['opacity']='0';const _0x3c06e0=()=>{const _0x25a2f9=_0x4d72b3;_0x513255['removeEventListener'](_0x25a2f9(0x143),_0x3c06e0),_0x444c98();};_0x513255[_0x4d72b3(0x166)](_0x4d72b3(0x143),_0x3c06e0,{'once':!0x0}),setTimeout(_0x3c06e0,0x55);}}));let _0x3668e9=Promise['resolve']();_0x28c02d&&_0x1d800f&&(_0x3668e9=new Promise(_0x52c9c3=>{const _0x416925=_0x5611fb;_0x1d800f[_0x416925(0x18a)]['transition']='opacity\x200.15s\x20ease-out',_0x1d800f['style'][_0x416925(0x177)]='0';const _0x24fd44=()=>{const _0x5dec08=_0x416925;_0x1d800f[_0x5dec08(0x14e)]('transitionend',_0x24fd44),_0x1d800f['parentNode']&&_0x1d800f['remove'](),_0x52c9c3();};_0x1d800f[_0x416925(0x166)](_0x416925(0x143),_0x24fd44,{'once':!0x0}),setTimeout(_0x24fd44,0xa0);})),Promise[_0x5611fb(0x1de)]([_0x1e562,_0x3668e9])['then'](()=>{const _0x35d26e=_0x5611fb;if(isDesktop()){const _0x16d2e0=allProjectPostsData[currentProjectIndex]||{};let _0x586e25=_0x3c9f33['querySelector']('.lightbox-desc-card');if(_0x28c02d)clearChildren(_0x3c9f33),_0x3c9f33['appendChild'](_0x1b6b57),_0x586e25=createDesktopDescriptionCard(_0x16d2e0['title'],_0x16d2e0['description'],_0x16d2e0['bulletPoints'],_0x16d2e0[_0x35d26e(0x15a)],currentProjectImages[_0x35d26e(0x1a6)],currentImageIndex),_0x3c9f33[_0x35d26e(0x178)](_0x586e25),_0x3c9f33['classList'][_0x35d26e(0x159)]('image-media-active','video-media-active'),_0x35d26e(0x1f9)===_0x130e15?_0x3c9f33[_0x35d26e(0x1cf)][_0x35d26e(0x170)]('video-media-active'):_0x35d26e(0x1b7)===_0x130e15&&_0x3c9f33[_0x35d26e(0x1cf)][_0x35d26e(0x170)]('image-media-active'),_0x586e25['style'][_0x35d26e(0x1ed)]=_0x35d26e(0x1a7),_0x586e25[_0x35d26e(0x1cf)][_0x35d26e(0x159)]('desc-card-overlay-mode',_0x35d26e(0x207)),userPrefersDescriptionVisible||(userPrefersDescriptionVisible=!0x0),_0x586e25[_0x35d26e(0x18a)][_0x35d26e(0x177)]='0',_0x586e25['style'][_0x35d26e(0x1f7)]='translateX(-40px)',_0x586e25[_0x35d26e(0x1c4)],_0x586e25[_0x35d26e(0x18a)]['transition']='opacity\x200.25s\x20ease-out,\x20transform\x200.25s\x20ease-out',_0x586e25['style'][_0x35d26e(0x177)]='1',_0x586e25['style'][_0x35d26e(0x1f7)]=_0x35d26e(0x14d),_0x3c9f33['classList'][_0x35d26e(0x170)](_0x35d26e(0x157)),setTimeout(()=>{const _0x31f147=_0x35d26e;_0x586e25&&(_0x586e25[_0x31f147(0x18a)]['transition']=_0x31f147(0x1a7));},0x104);else{const _0x1e3104=_0x3c9f33['querySelector']('img,\x20.video-outer-wrapper');_0x1e3104&&_0x3c9f33[_0x35d26e(0x1d1)](_0x1e3104),_0x3c9f33[_0x35d26e(0x203)](_0x1b6b57,_0x3c9f33[_0x35d26e(0x14f)]),_0x3c9f33['classList']['remove']('image-media-active',_0x35d26e(0x1a2)),_0x35d26e(0x1f9)===_0x130e15?_0x3c9f33['classList']['add']('video-media-active'):_0x35d26e(0x1b7)===_0x130e15&&_0x3c9f33['classList']['add'](_0x35d26e(0x184)),_0x586e25[_0x35d26e(0x18a)]['transition']='none',currentProjectImages[_0x35d26e(0x1a6)]>0x1?(updateActiveDot(currentImageIndex),setNavDotsVisibility(!0x0)):setNavDotsVisibility(!0x1),_0x586e25[_0x35d26e(0x1cf)][_0x35d26e(0x159)](_0x35d26e(0x13c),_0x35d26e(0x207)),userPrefersDescriptionVisible?(_0x586e25[_0x35d26e(0x1cf)][_0x35d26e(0x159)](_0x35d26e(0x207)),_0x586e25[_0x35d26e(0x18a)]['opacity']='1',_0x586e25['style'][_0x35d26e(0x1f7)]=_0x35d26e(0x14d),_0x3c9f33[_0x35d26e(0x1cf)]['add'](_0x35d26e(0x157))):(_0x586e25[_0x35d26e(0x18a)]['opacity']='0',_0x586e25['style'][_0x35d26e(0x1f7)]='translateX(-40px)','0'===_0x586e25[_0x35d26e(0x18a)][_0x35d26e(0x177)]&&_0x586e25[_0x35d26e(0x1cf)][_0x35d26e(0x170)](_0x35d26e(0x207)),_0x3c9f33[_0x35d26e(0x1cf)]['remove'](_0x35d26e(0x157)));}}else clearChildren(_0x3c9f33),_0x3c9f33['appendChild'](_0x1b6b57),_0x3c9f33['classList']['remove']('image-media-active',_0x35d26e(0x1a2)),'video'===_0x130e15?_0x3c9f33['classList']['add']('video-media-active'):'image'===_0x130e15&&_0x3c9f33[_0x35d26e(0x1cf)]['add']('image-media-active');_0x3c9f33['dataset'][_0x35d26e(0x151)]=_0x8edba9['toString'](),requestAnimationFrame(()=>{const _0x499273=_0x35d26e;if(_0x30775b&&!isDesktop()&&0x0!==_0x429f80){const _0x517da1=0x1===_0x429f80?'100vw':_0x499273(0x186);_0x1b6b57['style']['transition']='none',_0x1b6b57['style']['transform']='translateX('+_0x517da1+')',_0x1b6b57['style']['opacity']='1',requestAnimationFrame(()=>{const _0x4488ec=_0x499273;_0x1b6b57['style']['transition']='transform\x20250ms\x20cubic-bezier(0.4,0,0.2,1)',_0x1b6b57['style']['transform']=_0x4488ec(0x20a);});}else requestAnimationFrame(()=>{const _0x1e431b=_0x499273;_0x1b6b57['style']['transition']='opacity\x200.15s\x20ease-in',_0x1b6b57[_0x1e431b(0x18a)][_0x1e431b(0x177)]='1';});if(updateLightboxDetails(_0xfe0112,_0x328230||_0x3b6412),!isDesktop()){const _0xe5e1e3=document['getElementById'](_0x499273(0x1e1));_0xe5e1e3&&(userPrefersDescriptionVisible?setTimeout(()=>{const _0x3b7dfe=_0x499273;toggleMobileOverlays(_0xfe0112,_0x3b7dfe(0x15e));},0x32):setTimeout(()=>{hideMobileOverlays(_0xe5e1e3);},0x32));}updateToolbarNavState(),sendMessageToParent({'type':'lightbox-state','open':!0x0,'hasNext':currentProjectImages['length']>0x1,'hasPrevious':currentProjectImages[_0x499273(0x1a6)]>0x1,'descriptionState':userPrefersDescriptionVisible}),'function'==typeof window[_0x499273(0x16a)]&&window[_0x499273(0x16a)](!0x0);});})['catch'](_0x490289=>{const _0x24df7f=_0x5611fb;_0x513255&&_0x513255['parentNode']&&_0x513255[_0x24df7f(0x159)]();})['finally'](()=>{isLightboxTransitioning=!0x1;}),setTimeout(()=>updateMobileNavDots(_0x4d7470),0x64);}function updateActiveDot(_0x5b1493){const _0x2ad9e8=_0x2a7b,_0x11d04e=lightboxContentEl[_0x2ad9e8(0x13f)](_0x2ad9e8(0x17c));if(!_0x11d04e)return;const _0x13fa30=_0x11d04e['querySelector']('.lightbox-nav-dots');if(!_0x13fa30)return;_0x13fa30['querySelectorAll'](_0x2ad9e8(0x1f0))[_0x2ad9e8(0x1bb)]((_0x36642c,_0x414176)=>{const _0x3d2bae=_0x2ad9e8;_0x414176===_0x5b1493?_0x36642c[_0x3d2bae(0x1cf)]['add'](_0x3d2bae(0x187)):_0x36642c[_0x3d2bae(0x1cf)][_0x3d2bae(0x159)]('active');});}function setNavDotsVisibility(_0x5086f3){const _0x4b7daa=_0x2a7b,_0x49db90=lightboxContentEl[_0x4b7daa(0x13f)]('.lightbox-desc-card');if(!_0x49db90)return;const _0xc0bcc9=_0x49db90[_0x4b7daa(0x13f)](_0x4b7daa(0x19b));_0xc0bcc9&&(_0xc0bcc9['style'][_0x4b7daa(0x17b)]=_0x5086f3?_0x4b7daa(0x208):'none');}function _navigateNext(){const _0x3b1672=_0x2a7b;null!==currentProjectIndex&&allProjectPostsData[currentProjectIndex]&&0x0!==currentProjectImages[_0x3b1672(0x1a6)]&&(currentImageIndex<currentProjectImages['length']-0x1?currentImageIndex++:currentImageIndex=0x0,_openLightboxByProjectAndImage(currentProjectIndex,currentImageIndex,0x1,!0x0));}function _navigatePrevious(){const _0x41b268=_0x2a7b;null!==currentProjectIndex&&allProjectPostsData[currentProjectIndex]&&0x0!==currentProjectImages[_0x41b268(0x1a6)]&&(currentImageIndex>0x0?currentImageIndex--:currentImageIndex=currentProjectImages[_0x41b268(0x1a6)]-0x1,_openLightboxByProjectAndImage(currentProjectIndex,currentImageIndex,-0x1,!0x0));}function _closeLightbox(){const _0x5486fb=_0x2a7b;if(!lightboxEl||!lightboxContentEl)return;const _0xea4d68=lightboxOpenGeneration;lightboxEl[_0x5486fb(0x1cf)]['remove'](_0x5486fb(0x16d)),lightboxEl[_0x5486fb(0x1cf)]['add']('fade-out');let _0xf2c22d=!0x1;onLightboxCloseTransitionEnd=_0x2435e3=>{const _0x4c5adf=_0x5486fb;_0xea4d68===lightboxOpenGeneration&&_0x2435e3['target']===lightboxEl&&_0x4c5adf(0x177)===_0x2435e3[_0x4c5adf(0x18b)]&&(_0xf2c22d=!0x0,lightboxEl[_0x4c5adf(0x18a)]['display']='none',lightboxEl[_0x4c5adf(0x1cf)]['remove']('fade-out'),lightboxEl['removeEventListener'](_0x4c5adf(0x143),onLightboxCloseTransitionEnd),lightboxEl['style'][_0x4c5adf(0x1c1)]=_0x4c5adf(0x198),lightboxEl['style'][_0x4c5adf(0x177)]='',lightboxContentEl&&(lightboxContentEl['style']['opacity']=''),closeLightboxTimeoutId&&(clearTimeout(closeLightboxTimeoutId),closeLightboxTimeoutId=null),onLightboxCloseTransitionEnd=null);},lightboxEl['addEventListener'](_0x5486fb(0x143),onLightboxCloseTransitionEnd),closeLightboxTimeoutId&&clearTimeout(closeLightboxTimeoutId),closeLightboxTimeoutId=setTimeout(()=>{const _0x1c5964=_0x5486fb;_0xea4d68===lightboxOpenGeneration&&(!_0xf2c22d&&lightboxEl[_0x1c5964(0x1cf)][_0x1c5964(0x13b)]('fade-out')&&(lightboxEl['style']['display']=_0x1c5964(0x1a7),lightboxEl[_0x1c5964(0x1cf)][_0x1c5964(0x159)](_0x1c5964(0x13e)),onLightboxCloseTransitionEnd&&(lightboxEl[_0x1c5964(0x14e)](_0x1c5964(0x143),onLightboxCloseTransitionEnd),onLightboxCloseTransitionEnd=null),lightboxEl['style']['visibility']='hidden',lightboxEl['style'][_0x1c5964(0x177)]='',lightboxContentEl&&(lightboxContentEl[_0x1c5964(0x18a)]['opacity']='')),closeLightboxTimeoutId=null);},0x12c);const _0x4f3d3d=lightboxContentEl[_0x5486fb(0x13f)](_0x5486fb(0x200));_0x4f3d3d&&'VIDEO'===_0x4f3d3d['tagName']&&(_0x4f3d3d[_0x5486fb(0x1f3)](),_0x4f3d3d['removeAttribute'](_0x5486fb(0x1fd)),_0x4f3d3d['load']()),clearChildren(lightboxContentEl),lightboxDetailsEl&&clearChildren(lightboxDetailsEl),document[_0x5486fb(0x148)]['style'][_0x5486fb(0x173)]='',sendMessageToParent({'type':'set-home-enabled','enabled':!0x1}),lightboxEl['querySelectorAll']('.lightbox-title-overlay,\x20.lightbox-description-overlay')[_0x5486fb(0x1bb)](_0x51fa7f=>_0x51fa7f[_0x5486fb(0x159)]()),sendMessageToParent({'type':_0x5486fb(0x1a8),'open':!0x1}),_0x5486fb(0x1d4)==typeof window['handleLightboxStateChange']&&window['handleLightboxStateChange'](!0x1),currentProjectIndex=null,currentImageIndex=null,currentProjectImages=[];}function createLightboxOverlay(_0x5d9c85,_0x3f1670='bottom'){const _0x945387=_0x2a7b,_0x2a8e8b=_0x945387(0x14a)===_0x3f1670?'lightbox-title-overlay':_0x945387(0x1b4),_0x35fae4=createEl('div',_0x2a8e8b),_0x4b3da7=createEl('span','lightbox-title-overlay'===_0x2a8e8b?_0x945387(0x1a5):'');if(_0x4b3da7[_0x945387(0x1fe)]=_0x5d9c85||'',_0x35fae4[_0x945387(0x178)](_0x4b3da7),_0x945387(0x14a)===_0x3f1670&&currentProjectImages&&currentProjectImages['length']>0x1){const _0x630038=createMobileNavDots(currentProjectImages['length'],currentImageIndex);_0x35fae4['appendChild'](_0x630038);}return _0x35fae4;}function hideMobileOverlays(_0x26f83f){const _0x42c61c=_0x2a7b;if(!_0x26f83f)return!0x1;let _0x212138=!0x1;function _0x596170(){const _0x4a09e4=_0x2a7b;this['removeEventListener'](_0x4a09e4(0x1ca),_0x596170),this['parentNode']&&this['parentNode']['removeChild'](this);}const _0x150a39=_0x26f83f['querySelector']('.lightbox-title-overlay.show');_0x150a39&&(_0x150a39[_0x42c61c(0x18a)][_0x42c61c(0x1c6)]='',_0x150a39['style'][_0x42c61c(0x1ed)]='',_0x150a39['classList'][_0x42c61c(0x159)]('show'),_0x150a39['classList'][_0x42c61c(0x170)](_0x42c61c(0x19e)),_0x150a39[_0x42c61c(0x18a)][_0x42c61c(0x1b2)]=_0x42c61c(0x1a7),_0x150a39[_0x42c61c(0x166)]('animationend',_0x596170,{'once':!0x0}),_0x212138=!0x0);const _0x53c103=_0x26f83f[_0x42c61c(0x13f)](_0x42c61c(0x1c9));return _0x53c103&&(_0x53c103[_0x42c61c(0x18a)]['animation']='',_0x53c103['style'][_0x42c61c(0x1ed)]='',_0x53c103['classList']['remove']('show'),_0x53c103['classList']['add'](_0x42c61c(0x19e)),_0x53c103['style']['pointerEvents']=_0x42c61c(0x1a7),_0x53c103[_0x42c61c(0x166)]('animationend',_0x596170,{'once':!0x0}),_0x212138=!0x0),_0x212138&&(userPrefersDescriptionVisible=!0x1,window[_0x42c61c(0x192)]&&sessionStorage[_0x42c61c(0x209)](_0x42c61c(0x17f),_0x42c61c(0x147)),sendMessageToParent({'type':'description-state','open':!0x1})),_0x212138;}function toggleMobileOverlays(_0x33bb44,_0x2f5e51='toggle'){const _0x14c2d0=_0x2a7b,_0x56f96f=document[_0x14c2d0(0x1fb)]('lightbox-inner-wrapper');if(!_0x56f96f||isDesktop())return;const _0x69cefe=function(_0x398096){_0x398096['stopPropagation'](),hideMobileOverlays(_0x56f96f);},_0x16d2da=_0x56f96f[_0x14c2d0(0x13f)](_0x14c2d0(0x14c)),_0x5caa24=_0x56f96f[_0x14c2d0(0x13f)]('.lightbox-description-overlay.show'),_0x311378=_0x16d2da||_0x5caa24;if('toggle'===_0x2f5e51?!_0x311378:'show'===_0x2f5e51){let _0x2cdf47=_0x56f96f[_0x14c2d0(0x13f)](_0x14c2d0(0x190));_0x2cdf47&&_0x2cdf47['classList']['contains'](_0x14c2d0(0x15e))||(_0x2cdf47&&_0x2cdf47['remove'](),_0x2cdf47=createLightboxOverlay(_0x33bb44,'top'),_0x56f96f[_0x14c2d0(0x178)](_0x2cdf47),_0x2cdf47[_0x14c2d0(0x1c4)],_0x2cdf47['classList'][_0x14c2d0(0x159)](_0x14c2d0(0x19e)),_0x2cdf47[_0x14c2d0(0x1cf)]['add'](_0x14c2d0(0x15e)),_0x2cdf47['style']['pointerEvents']=_0x14c2d0(0x19c)),_0x2cdf47&&(_0x2cdf47['removeEventListener']('click',_0x69cefe),_0x2cdf47['addEventListener'](_0x14c2d0(0x1d7),_0x69cefe));let _0x3646b3=_0x56f96f[_0x14c2d0(0x13f)](_0x14c2d0(0x1bd));const _0x28e40b=allProjectPostsData[currentProjectIndex]||{},_0x19c482=_0x28e40b['mobileDescription']||_0x28e40b['description']||'';_0x3646b3&&_0x3646b3[_0x14c2d0(0x1cf)][_0x14c2d0(0x13b)](_0x14c2d0(0x15e))||(_0x3646b3&&_0x3646b3['remove'](),_0x3646b3=createLightboxOverlay(_0x19c482,'bottom'),_0x56f96f['appendChild'](_0x3646b3),_0x3646b3[_0x14c2d0(0x1c4)],_0x3646b3['classList']['remove']('hide-anim'),_0x3646b3[_0x14c2d0(0x1cf)]['add']('show'),_0x3646b3[_0x14c2d0(0x18a)]['pointerEvents']=_0x14c2d0(0x19c)),_0x3646b3&&(_0x3646b3[_0x14c2d0(0x14e)](_0x14c2d0(0x1d7),_0x69cefe),_0x3646b3['addEventListener'](_0x14c2d0(0x1d7),_0x69cefe));const _0x13af54=_0x56f96f['querySelector']('.lightbox-title-overlay.show'),_0x51b92f=_0x56f96f['querySelector']('.lightbox-description-overlay.show');(_0x13af54||_0x51b92f)&&(userPrefersDescriptionVisible||(userPrefersDescriptionVisible=!0x0,window[_0x14c2d0(0x192)]&&sessionStorage[_0x14c2d0(0x209)]('projectsUserPrefersDescriptionVisible',_0x14c2d0(0x188)),sendMessageToParent({'type':'description-state','open':!0x0}))),updateMobileNavDots(currentImageIndex);}else _0x311378&&hideMobileOverlays(_0x56f96f);}function toggleDesktopDescriptionOverlay(_0x5d25d5){const _0x5255a7=_0x2a7b;if(!_0x5d25d5||!isDesktop())return;let _0x483ed7=_0x5d25d5['querySelector'](_0x5255a7(0x17c)),_0x358d99=!userPrefersDescriptionVisible;if(!_0x483ed7&&_0x358d99){const _0x156164=allProjectPostsData[currentProjectIndex]||{};_0x483ed7=createDesktopDescriptionCard(_0x156164[_0x5255a7(0x14b)],_0x156164[_0x5255a7(0x17a)],_0x156164[_0x5255a7(0x1bf)],_0x156164['toolsUsed'],currentProjectImages[_0x5255a7(0x1a6)],currentImageIndex),_0x5d25d5['appendChild'](_0x483ed7),_0x483ed7['style']['transition']=_0x5255a7(0x1a7),_0x483ed7['classList'][_0x5255a7(0x159)]('fully-hidden'),_0x483ed7[_0x5255a7(0x1cf)]['remove'](_0x5255a7(0x13c)),_0x483ed7[_0x5255a7(0x18a)][_0x5255a7(0x177)]='0',_0x483ed7['style'][_0x5255a7(0x1f7)]='translateX(-40px)',_0x483ed7['classList'][_0x5255a7(0x170)](_0x5255a7(0x207)),_0x483ed7['offsetWidth'];}_0x483ed7&&(_0x358d99?(userPrefersDescriptionVisible=!0x0,_0x5d25d5[_0x5255a7(0x1cf)]['add']('desc-visible'),_0x483ed7[_0x5255a7(0x1cf)]['remove']('fully-hidden'),_0x483ed7[_0x5255a7(0x1c4)],_0x483ed7['style']['transition']='opacity\x200.25s\x20ease-out,\x20transform\x200.25s\x20ease-out',_0x483ed7[_0x5255a7(0x18a)][_0x5255a7(0x177)]='1',_0x483ed7['style'][_0x5255a7(0x1f7)]='translateX(0px)'):(userPrefersDescriptionVisible=!0x1,_0x5d25d5[_0x5255a7(0x1cf)][_0x5255a7(0x159)](_0x5255a7(0x157)),_0x483ed7['style'][_0x5255a7(0x1ed)]=_0x5255a7(0x1ea),_0x483ed7[_0x5255a7(0x18a)]['opacity']='0',_0x483ed7['style']['transform']=_0x5255a7(0x160),setTimeout(()=>{const _0x5dc619=_0x5255a7;_0x483ed7&&'0'===_0x483ed7['style'][_0x5dc619(0x177)]&&_0x483ed7[_0x5dc619(0x1cf)]['add'](_0x5dc619(0x207));},0x104)),window['sessionStorage']&&sessionStorage[_0x5255a7(0x209)](_0x5255a7(0x17f),userPrefersDescriptionVisible[_0x5255a7(0x142)]()),sendMessageToParent({'type':'description-state','open':_0x358d99}),setTimeout(()=>{const _0x2476c6=_0x5255a7;_0x483ed7&&(_0x483ed7[_0x2476c6(0x18a)][_0x2476c6(0x1ed)]='none');},0x104));}function throttleNavigationButtons(){isNavigationThrottled=!0x0,sendMessageToParent({'type':'throttle-nav-buttons','active':!0x0}),setTimeout(()=>{const _0xc25f34=_0x2a7b;isNavigationThrottled=!0x1,sendMessageToParent({'type':_0xc25f34(0x1cd),'active':!0x1}),lightboxEl&&'flex'===lightboxEl[_0xc25f34(0x18a)][_0xc25f34(0x17b)]&&updateToolbarNavState();},0x12c);}let dragStartX=0x0,dragCurrentX=0x0,dragStartY=0x0,dragCurrentY=0x0,dragging=!0x1,dragHasMoved=!0x1,dragRAF=null,dragIsVertical=!0x1;function setSwipeContentTransform(_0x272a6f,_0x2a0ada,_0x33c91c=0x1,_0x74ebc8=!0x1){const _0x2e9696=_0x2a7b;if(!lightboxContentEl)return;const _0x43517a=lightboxContentEl[_0x2e9696(0x13f)](_0x2e9696(0x1eb)),_0x4bcb32=document['getElementById']('lightbox-inner-wrapper'),_0x136ac2=_0x4bcb32?.['querySelector']('.lightbox-title-overlay.show'),_0x2d85b0=_0x4bcb32?.['querySelector']('.lightbox-description-overlay.show');let _0x5b940b=_0x33c91c;if(_0x43517a&&(_0x43517a[_0x2e9696(0x18a)][_0x2e9696(0x177)]=''),lightboxEl&&(lightboxEl[_0x2e9696(0x18a)][_0x2e9696(0x177)]=''),_0x136ac2&&(_0x136ac2[_0x2e9696(0x18a)]['transition']='none'),_0x2d85b0&&(_0x2d85b0[_0x2e9696(0x18a)]['transition']=_0x2e9696(0x1a7)),_0x43517a&&dragIsVertical){if(!_0x74ebc8&&_0x2a0ada<0x0){const _0x5a1f3c=0.95,_0x5e7fcc=Math['min'](0x1,Math['abs'](_0x2a0ada)/(window['innerHeight']*_0x5a1f3c)),_0x1b5d6f=0x1-Math[_0x2e9696(0x1b0)](_0x5e7fcc,0x2);_0x43517a['style'][_0x2e9696(0x177)]=_0x1b5d6f,lightboxEl&&(lightboxEl['style']['opacity']=_0x1b5d6f),_0x136ac2&&(_0x136ac2[_0x2e9696(0x18a)]['opacity']=_0x1b5d6f),_0x2d85b0&&(_0x2d85b0['style'][_0x2e9696(0x177)]=_0x1b5d6f),_0x5b940b=0x1-0.75*_0x5e7fcc;}else{if(_0x74ebc8&&_0x2a0ada>0x0){const _0x69d3f6=0x1-0.5*Math[_0x2e9696(0x191)](0x1,_0x2a0ada/0x28);_0x136ac2&&(_0x136ac2[_0x2e9696(0x18a)]['opacity']=_0x69d3f6),_0x2d85b0&&(_0x2d85b0[_0x2e9696(0x18a)][_0x2e9696(0x177)]=_0x69d3f6);}}}lightboxContentEl&&(lightboxContentEl['style']['transform']='translateX('+_0x272a6f+'px)\x20translateY('+_0x2a0ada+'px)\x20scale('+_0x5b940b+')');}function handleTouchStart(_0x20c037){const _0x183a67=_0x2a7b;if(0x1!==_0x20c037[_0x183a67(0x197)][_0x183a67(0x1a6)]||!lightboxContentEl)return;dragStartX=_0x20c037[_0x183a67(0x197)][0x0]['clientX'],dragCurrentX=dragStartX,dragStartY=_0x20c037['touches'][0x0]['clientY'],dragCurrentY=dragStartY,dragging=!0x0,dragHasMoved=!0x1,dragIsVertical=!0x1,lightboxContentEl[_0x183a67(0x1cf)]['add'](_0x183a67(0x1c5)),lightboxContentEl['classList']['remove'](_0x183a67(0x158));const _0x2d7352=document['getElementById'](_0x183a67(0x1e1)),_0x1d46f9=_0x2d7352?.[_0x183a67(0x13f)]('.lightbox-title-overlay.show'),_0x42ad0d=_0x2d7352?.['querySelector'](_0x183a67(0x1c9));_0x1d46f9&&(_0x1d46f9['style']['opacity']='1',_0x1d46f9[_0x183a67(0x18a)]['animation']=_0x183a67(0x1a7),_0x1d46f9[_0x183a67(0x18a)][_0x183a67(0x1ed)]='none'),_0x42ad0d&&(_0x42ad0d['style']['opacity']='1',_0x42ad0d['style']['animation']='none',_0x42ad0d['style'][_0x183a67(0x1ed)]='none');}function handleTouchMove(_0x2f7cae){const _0x54c835=_0x2a7b;if(!dragging||0x1!==_0x2f7cae['touches']['length']||!lightboxContentEl)return;dragCurrentX=_0x2f7cae[_0x54c835(0x197)][0x0][_0x54c835(0x18c)],dragCurrentY=_0x2f7cae[_0x54c835(0x197)][0x0]['clientY'];const _0x2558c2=dragCurrentX-dragStartX,_0x5982eb=dragCurrentY-dragStartY;if((Math[_0x54c835(0x1ee)](_0x2558c2)>0x8||Math[_0x54c835(0x1ee)](_0x5982eb)>0x8)&&(dragHasMoved=!0x0),!dragIsVertical&&Math[_0x54c835(0x1ee)](_0x5982eb)>Math['abs'](_0x2558c2)&&Math['abs'](_0x5982eb)>0xc?dragIsVertical=!0x0:!dragIsVertical&&Math['abs'](_0x2558c2)>Math[_0x54c835(0x1ee)](_0x5982eb)&&Math['abs'](_0x2558c2)>0xc&&(dragIsVertical=!0x1),dragRAF&&cancelAnimationFrame(dragRAF),dragIsVertical){let _0x241d5c=_0x5982eb,_0x270b1c=0x1,_0x1529e0=!0x1;_0x5982eb<0x0?_0x270b1c=0x1+(0.85-0x1)*Math[_0x54c835(0x191)](0x1,Math['abs'](_0x241d5c)/(window[_0x54c835(0x1b8)]/0x2)):_0x5982eb>0x0?(_0x241d5c=Math['min'](_0x5982eb,0x28),_0x1529e0=!0x0):_0x241d5c=0x0,dragRAF=requestAnimationFrame(()=>setSwipeContentTransform(0x0,_0x241d5c,_0x270b1c,_0x1529e0));}else dragRAF=requestAnimationFrame(()=>setSwipeContentTransform(_0x2558c2,0x0,0x1,!0x1));}function handleTouchEnd(_0x16f2aa){const _0x69c7c7=_0x2a7b;if(!dragging||!lightboxContentEl)return;dragging=!0x1,dragRAF&&cancelAnimationFrame(dragRAF);const _0x47cd98=dragCurrentX-dragStartX,_0x5abeaf=dragCurrentY-dragStartY,_0x2928c5=lightboxContentEl['querySelector'](_0x69c7c7(0x1eb));if(!dragHasMoved){let _0x33a44b=!0x1;if(_0x2928c5){const _0x572ad4=_0x2928c5[_0x69c7c7(0x204)]('img,\x20video')?_0x2928c5:_0x2928c5[_0x69c7c7(0x13f)]('video');_0x572ad4&&_0x16f2aa['target']['closest']('.mute-icon-overlay,\x20.video-spinner-overlay,\x20video,\x20img')===_0x572ad4['closest']('.video-outer-wrapper,\x20img')&&(_0x33a44b=!0x0);}return _0x33a44b?(_0x16f2aa&&_0x16f2aa['stopPropagation'](),void(lightboxContentEl['style']['transform']='translateX(0)\x20translateY(0)\x20scale(1)')):void(lightboxContentEl['style'][_0x69c7c7(0x1f7)]='translateX(0)\x20translateY(0)\x20scale(1)');}let _0x5cac52=!0x1,_0x5e7cc2=!0x1,_0x12f07e=!0x1;const _0x34889a=currentProjectImages&&currentProjectImages['length']>0x1;if(_0x2928c5){const _0x4f49fa=_0x2928c5[_0x69c7c7(0x201)]();dragIsVertical&&Math[_0x69c7c7(0x1ee)](_0x5abeaf)>0x2c&&_0x4f49fa[_0x69c7c7(0x1f5)]+_0x5abeaf<window['innerHeight']/0x2&&Math['abs'](_0x5abeaf)>Math[_0x69c7c7(0x1ee)](_0x47cd98)&&(_0x5cac52=!0x0),!_0x5cac52&&_0x34889a&&(!dragIsVertical&&Math[_0x69c7c7(0x1ee)](_0x47cd98)>0x2c&&_0x4f49fa[_0x69c7c7(0x1df)]+_0x47cd98<window['innerWidth']/0x2&&_0x47cd98<0x0&&Math[_0x69c7c7(0x1ee)](_0x47cd98)>Math[_0x69c7c7(0x1ee)](_0x5abeaf)?_0x5e7cc2=!0x0:!dragIsVertical&&Math[_0x69c7c7(0x1ee)](_0x47cd98)>0x2c&&_0x4f49fa['left']+_0x47cd98>window['innerWidth']/0x2&&_0x47cd98>0x0&&Math[_0x69c7c7(0x1ee)](_0x47cd98)>Math['abs'](_0x5abeaf)&&(_0x12f07e=!0x0));}if(_0x5cac52){lightboxContentEl[_0x69c7c7(0x1cf)]['remove'](_0x69c7c7(0x1c5)),lightboxContentEl[_0x69c7c7(0x1cf)][_0x69c7c7(0x170)](_0x69c7c7(0x158));const _0x126924=-window[_0x69c7c7(0x1b8)],_0x1771b6=0x190,_0x1ba118=0.25;lightboxContentEl['style'][_0x69c7c7(0x1ed)]='transform\x20'+_0x1771b6+_0x69c7c7(0x167),lightboxContentEl['style'][_0x69c7c7(0x1f7)]=_0x69c7c7(0x179)+_0x126924+_0x69c7c7(0x1f8)+_0x1ba118+')',lightboxEl&&(lightboxEl[_0x69c7c7(0x18a)]['transition']='opacity\x20'+_0x1771b6+_0x69c7c7(0x167)),lightboxEl&&(lightboxEl['style']['opacity']=0x0),_0x2928c5&&(_0x2928c5[_0x69c7c7(0x18a)]['transition']='opacity\x20'+_0x1771b6+_0x69c7c7(0x167)),_0x2928c5&&(_0x2928c5['style']['opacity']=0x0);const _0x589089=()=>{const _0x4daa62=_0x69c7c7;lightboxContentEl['removeEventListener'](_0x4daa62(0x143),_0x589089),lightboxContentEl['classList']['remove'](_0x4daa62(0x158)),lightboxContentEl['style']['transition']='',lightboxContentEl[_0x4daa62(0x18a)]['transform']=_0x4daa62(0x146),lightboxEl&&(lightboxEl[_0x4daa62(0x18a)]['transition']=''),lightboxEl&&(lightboxEl['style']['opacity']=''),_0x2928c5&&(_0x2928c5[_0x4daa62(0x18a)][_0x4daa62(0x1ed)]='',_0x2928c5['style'][_0x4daa62(0x177)]=''),_closeLightbox();};return void lightboxContentEl['addEventListener'](_0x69c7c7(0x143),_0x589089);}if(_0x5e7cc2||_0x12f07e){const _0x18c1f3=_0x5e7cc2?-window[_0x69c7c7(0x141)]:window['innerWidth'],_0x16487c=0x190,_0x1f844e=lightboxContentEl['querySelector'](_0x69c7c7(0x195));lightboxContentEl[_0x69c7c7(0x1cf)][_0x69c7c7(0x170)]('animate'),lightboxContentEl['style'][_0x69c7c7(0x1ed)]=_0x69c7c7(0x1a0)+_0x16487c+_0x69c7c7(0x167),lightboxContentEl['style']['transform']='translateX('+_0x18c1f3+_0x69c7c7(0x183),lightboxEl&&(lightboxEl[_0x69c7c7(0x18a)][_0x69c7c7(0x1ed)]=''),lightboxEl&&(lightboxEl['style']['opacity']='');const _0x303d7f=()=>{const _0x247e70=_0x69c7c7;lightboxContentEl[_0x247e70(0x14e)](_0x247e70(0x143),_0x303d7f),lightboxContentEl['classList']['remove'](_0x247e70(0x158)),_0x1f844e&&_0x1f844e[_0x247e70(0x18e)]&&_0x1f844e[_0x247e70(0x18e)][_0x247e70(0x1d1)](_0x1f844e),lightboxContentEl['style'][_0x247e70(0x1ed)]='none',lightboxContentEl['style']['transform']=_0x247e70(0x20a),lightboxContentEl[_0x247e70(0x1c4)],lightboxContentEl[_0x247e70(0x18a)][_0x247e70(0x1ed)]='',_0x5e7cc2?_navigateNext():_navigatePrevious();};return void lightboxContentEl[_0x69c7c7(0x166)](_0x69c7c7(0x143),_0x303d7f);}lightboxContentEl['classList']['remove'](_0x69c7c7(0x1c5)),lightboxContentEl['classList']['add']('animate'),lightboxContentEl['style']['transition']=_0x69c7c7(0x1dc),lightboxContentEl['style']['transform']='translateX(0)\x20translateY(0)\x20scale(1)',_0x2928c5&&(_0x2928c5['style'][_0x69c7c7(0x1ed)]='',_0x2928c5[_0x69c7c7(0x18a)]['opacity']=''),lightboxEl&&(lightboxEl[_0x69c7c7(0x18a)]['transition']=''),lightboxEl&&(lightboxEl['style']['opacity']='');const _0x1ed2c8=document['getElementById'](_0x69c7c7(0x1e1));if(_0x1ed2c8){const _0x590e6b=_0x1ed2c8['querySelector']('.lightbox-title-overlay'),_0xd0878c=_0x1ed2c8[_0x69c7c7(0x13f)](_0x69c7c7(0x1bd)),_0x4193e5=_0x163829=>{const _0x4d354d=_0x69c7c7;_0x163829&&(_0x163829['style']['transition']='none',_0x163829['style']['animation']='',_0x163829['classList'][_0x4d354d(0x13b)](_0x4d354d(0x15e))&&!_0x163829['classList']['contains']('hide-anim')?(_0x163829['style'][_0x4d354d(0x177)]='1',_0x163829[_0x4d354d(0x18a)]['transform']=_0x4d354d(0x1a9)):(_0x163829['style']['opacity']='',_0x163829['style']['transform']=''),requestAnimationFrame(()=>{const _0x9963f3=_0x4d354d;_0x163829&&(_0x163829['style'][_0x9963f3(0x1ed)]='');}));};_0x4193e5(_0x590e6b),_0x4193e5(_0xd0878c);}const _0x2f3930=_0x3d789a=>{const _0x27beec=_0x69c7c7;_0x3d789a[_0x27beec(0x16c)]===lightboxContentEl&&'transform'===_0x3d789a['propertyName']&&(lightboxContentEl['removeEventListener']('transitionend',_0x2f3930),lightboxContentEl[_0x27beec(0x18a)]['transition']='',lightboxContentEl[_0x27beec(0x1cf)][_0x27beec(0x159)]('animate'));};lightboxContentEl[_0x69c7c7(0x166)](_0x69c7c7(0x143),_0x2f3930);}function createMobileNavDots(_0x1d5ab1,_0x4d5c61){const _0x476b7a=_0x2a7b,_0x4878b0=createEl('div','mobile-lightbox-nav-dots');for(let _0x18ae27=0x0;_0x18ae27<_0x1d5ab1;_0x18ae27++){const _0x395478=createEl('span','mobile-lightbox-nav-dot');_0x18ae27===_0x4d5c61&&_0x395478['classList']['add'](_0x476b7a(0x187)),_0x395478[_0x476b7a(0x1c8)][_0x476b7a(0x1da)]=_0x18ae27,_0x395478['addEventListener'](_0x476b7a(0x1d7),_0x423764=>{const _0x6a34de=_0x476b7a;_0x423764['stopPropagation']();const _0xa648a5=parseInt(_0x395478['dataset'][_0x6a34de(0x1da)],0xa);_0xa648a5===currentImageIndex||isLightboxTransitioning||isNavigationThrottled||_openLightboxByProjectAndImage(currentProjectIndex,_0xa648a5,0x0,!0x1);}),_0x395478['addEventListener'](_0x476b7a(0x1ce),_0x1211d5=>{const _0x229d00=_0x476b7a;if('Enter'===_0x1211d5['key']||'\x20'===_0x1211d5['key']){_0x1211d5['preventDefault']();const _0x598995=parseInt(_0x395478[_0x229d00(0x1c8)]['index'],0xa);_0x598995===currentImageIndex||isLightboxTransitioning||isNavigationThrottled||_openLightboxByProjectAndImage(currentProjectIndex,_0x598995,0x0,!0x1);}}),_0x4878b0['appendChild'](_0x395478);}return _0x4878b0;}function updateMobileNavDots(_0x2eebd7){const _0x263895=_0x2a7b,_0x2e4bdf=document['querySelector']('.lightbox-title-overlay.show');if(!_0x2e4bdf)return;_0x2e4bdf[_0x263895(0x163)]('.mobile-lightbox-nav-dot')['forEach']((_0x2b0187,_0x26cd9e)=>{const _0x3f33e5=_0x263895;_0x26cd9e===_0x2eebd7?_0x2b0187['classList'][_0x3f33e5(0x170)](_0x3f33e5(0x187)):_0x2b0187[_0x3f33e5(0x1cf)][_0x3f33e5(0x159)](_0x3f33e5(0x187));});}function _0x4c61(){const _0x4df9e7=['C3jJtw9IAwXL','DhjHBNnMB3jTia','yxv0B3bSyxK','DMLKzw8TBwvKAweTywn0AxzL','DhjHBNnMB3jTidi1mg1Zign1yMLJlwjLEMLLCIGWlJqSmcWWlJiSmsK','Cgf1C2vK','BgLNAhrIB3GTB3zLCMXHEs10AxrSzs10zxH0','BgvUz3rO','BM9Uzq','BgLNAhrIB3GTC3rHDgu','DhjHBNnSyxrLwsGWChGP','q0Xjru5uifDpuKS','y3jLyxrLrwXLBwvUDa','Bwf4','BgLNAhrIB3GTzgvZyY1JyxjK','AxnbCNjHEq','Bwf4sgvPz2H0','Cg93','lM11DguTAwnVBI1VDMvYBgf5','Cg9PBNrLCKv2zw50CW','C3rHCNrZv2L0Aa','BgLNAhrIB3GTzgvZy3jPChrPB24TB3zLCMXHEq','mZu1nJm1mhrAs1njBW','DM9Szg93BM1VyG','Aw1Hz2u','Aw5UzxjizwLNAhq','DMvYDgLJywXbBgLNBG','zMLSDgvY','zM9YrwfJAa','B250B3vJAhn0yxj0','lMXPz2H0yM94lwrLC2nYAxb0Aw9Ulw92zxjSyxK','y2fYzc10AxrSzs1YB3C','yNvSBgv0ug9PBNrZ','C3bSAxq','DMLZAwjPBgL0Eq','y2fYzc1KzxnJCMLWDgLVBI1SywjLBa','nJa2zgvODLjh','B2zMC2v0v2LKDgG','C3DPCgLUzW','yw5PBwf0Aw9U','Bw91C2vSzwf2zq','zgf0yxnLDa','lMXPz2H0yM94lwrLC2nYAxb0Aw9Ulw92zxjSyxKUC2HVDW','yw5PBwf0Aw9Uzw5K','BwfW','DMLKzw8TC3bPBM5LCI1VDMvYBgf5','DgHYB3r0BguTBMf2lwj1DhrVBNm','A2v5zg93BG','y2XHC3nmAxn0','CMvHzhLtDgf0zq','CMvTB3zLq2HPBgq','rw50zxi','mtu0oty4ntDvv2fYDg8','zNvUy3rPB24','C3rVCfbYB3bHz2f0Aw9U','ChjLBg9Hza','y2XPy2S','mJq0mJu5mMX3rNbcyG','BxnnyxHuB3vJAfbVAw50CW','Aw5KzxG','AM9PBG','DhjHBNnMB3jTiduWmg1Zign1yMLJlwjLEMLLCIGWlJe3nsWGmc44oduSidaUmZiSideUmJC1kq','zgL2','ywXS','CMLNAhq','Bw91C2vLBNrLCG','BgLNAhrIB3GTAw5UzxiTD3jHChbLCG','BgLNAhrIB3GTBMf2lwrVDa','ChjVAMvJDhnvC2vYuhjLzMvYC011DgvK','y2fYzc10B29SCY1SywjLBa','Bwf4v2LKDgG','yxjPys1OAwrKzw4','BgLNAhrIB3GTy29UDgvUDa','CgfYzw50','kg1PBI13Awr0AdOGnZy4ChGP','B3bHy2L0EsaWlJi1CYbLyxnLlw91DcWGDhjHBNnMB3jTidaUmJvZigvHC2uTB3v0','Aw1Nlcb2AwrLBYWGlNzPzgvVlw91DgvYlxDYyxbWzxi','CgfYzw50rwXLBwvUDa','DhjHBNnPDgLVBG','ywjZ','revtq1jjufrjt04','lMXPz2H0yM94lw5HDI1KB3q','Bwf0y2HnzwrPyq','Bxv0zs1Py29Ulw92zxjSyxK','Cgf1C2u','mtaWDNC','yM90Dg9T','C2v0lwHVBwuTzw5HyMXLza','DhjHBNnMB3jT','ChGPihnJywXLka','DMLKzw8','Aw5SAw5LlwjSB2nR','z2v0rwXLBwvUDej5swq','y2fYzc10AxrSzs1KAxzPzgvY','C3jJ','Aw5Uzxjive1m','Ahr0CdO','DMLKzw8SigLTzW','z2v0qM91BMrPBMDdBgLLBNrszwn0','y2XVC2vZDa','Aw5Zzxj0qMvMB3jL','Bwf0y2HLCW','DhLWzq','C2v0qxr0CMLIDxrL','zNvSBhKTAgLKzgvU','zMXLEa','C2v0sxrLBq','DhjHBNnSyxrLwcGWkq','oMHVDMvY','lMXPz2H0yM94lw1LzgLHlxDYyxbWzxi','Dg91y2HLBMq','zgLZywjSzvbPy3r1CMvjBLbPy3r1CMu','tgLNAhrIB3GGBM90igLUAxrPywXPEMvKig9YierptsbUB3qGCMvHzhKGzM9Yig9Wzw4Oks4Gq2fSBcbmAwDODgjVEc5PBML0kcKGzMLYC3qU','y29UDgfPBNm','zgvZyY1JyxjKlw92zxjSyxKTBw9Kzq','Bxv0zwq','zMfKzs1VDxq','CxvLCNLtzwXLy3rVCG','DhjPBq','Aw5UzxjxAwr0Aa','Dg9tDhjPBMC','DhjHBNnPDgLVBMvUza','yxjPys1SywjLBa','nJuZrLzHse1v','DhjHBNnSyxrLwcGWksb0CMfUC2XHDgvzkdaPihnJywXLkdeP','zMfSC2u','yM9KEq','mtbgDfv4DuS','Dg9W','DgL0Bgu','lMXPz2H0yM94lxrPDgXLlw92zxjSyxKUC2HVDW','DhjHBNnSyxrLwcGWChGP','CMvTB3zLrxzLBNrmAxn0zw5LCG','zMLYC3rdAgLSza','vw5TDxrLza','Bg9HzgvKuhjVAMvJDeLUzgv4','y3vYCMvUDfrHCMDLDa','uevsu09oquWGv09ssW','Cg9ZDgvYtw9IAwXL','CgXHEwLUzW','y2XHC3noyw1L','zgvZyY12AxnPyMXL','yw5PBwf0zq','CMvTB3zL','Dg9VBhnvC2vK','lI4VlI4VlI4V','zMXLEeDYB3C','zxjYB3i','C2HVDW','DgfIAw5KzxG','DhjHBNnSyxrLwcGTndbWEcK','y2fYzc10AxrSzq','mtK5mefRvK5rDG','CxvLCNLtzwXLy3rVCKfSBa','D29YA1r5Cgu','uhjVAMvJDcbmAwDODgjVEcbwAwrLBW','ywrKrxzLBNrmAxn0zw5LCG','BxmGy3vIAwmTyMv6AwvYkdaUncWWldaUmIWXkq','z2v0sxrLBq','B3bHy2L0EsaWlJa3nxmGzwfZzs1VDxq','AgfUzgXLtgLNAhrIB3HtDgf0zunOyw5Nzq','Dgv4DenVBNrLBNq','DgfYz2v0','zMfKzs1PBG','ntq2nhHOzuPLqG','BgLNAhrIB3GTBMf2lwrVDhm','ywrK','Ahr0Chm6','A2v5','B3zLCMzSB3C','ndm1nZK3mfzZzunHAG','Cg9ZDgvY','CM9Szq','B3bHy2L0Eq','yxbWzw5Kq2HPBgq','DhjHBNnSyxrLwsG','zgvZy3jPChrPB24','zgLZCgXHEq','lMXPz2H0yM94lwrLC2mTy2fYza','sw1Hz2u','zMXLEfnOCMLUAW','ChjVAMvJDhnvC2vYuhjLzMvYC0rLC2nYAxb0Aw9UvMLZAwjSzq','y2fYzc10AxrSzs1TywLUlwXHyMvS','CMvZB2X2zq','Dg91y2HTB3zL','ChGP','Aw1Hz2uTBwvKAweTywn0AxzL','ndy5nJm1mhj6Dg54ta','lteWmhz3','ywn0AxzL','Dhj1zq','tgLNAhrIB3G','C3r5Bgu','ChjVCgvYDhLoyw1L','y2XPzw50wa','Cg9ZAxrPB24','CgfYzw50tM9Kzq','tgLNAhrIB3G6ievYCM9YihbHCNnPBMCGAw1Hz2vZigrHDgeUiezHBgXPBMCGyMfJAYb0BYbTywLUihnYyY4','lMXPz2H0yM94lxrPDgXLlw92zxjSyxK','BwLU','C2vZC2LVBLn0B3jHz2u','Dg91y2HZDgfYDa','pgrPDIbJBgfZCZ0IDMLKzw8TC3bPBM5LCIi+pc9KAxy+','Aw1NlcaUDMLKzw8TB3v0zxiTD3jHChbLCG','ve9ptfmGvvnfra','Dg91y2HLCW','AgLKzgvU','y2HHCKf0','D2fYBG','lMXPz2H0yM94lw5HDI1KB3rZ','yxv0BW','pgLTzYbZCMm9iI4UlY4UlY4Ul2fZC2v0CY9HChbZl3bYB2PLy3rZl2LJB25ZlW','AgLKzs1HBMLT'];_0x4c61=function(){return _0x4df9e7;};return _0x4c61();}window[_0x5912c6(0x189)]={'init':_0x36d6ff=>{const _0x164fb7=_0x5912c6;if(allProjectPostsData=_0x36d6ff,lightboxEl=document[_0x164fb7(0x1fb)]('project-lightbox'),lightboxContentEl=document['getElementById'](_0x164fb7(0x1e7)),lightboxDetailsEl=document['getElementById']('lightbox-details'),lightboxEl&&lightboxContentEl&&lightboxDetailsEl){if(window['sessionStorage']){null!==sessionStorage[_0x164fb7(0x168)]('projectsUserPrefersMuted')&&(userPrefersMuted='true'===sessionStorage['getItem'](_0x164fb7(0x1e3)));const _0x4ab9cd=sessionStorage[_0x164fb7(0x168)](_0x164fb7(0x17f));null!==_0x4ab9cd?userPrefersDescriptionVisible=_0x164fb7(0x188)===_0x4ab9cd:(userPrefersDescriptionVisible=isDesktop(),sessionStorage['setItem'](_0x164fb7(0x17f),userPrefersDescriptionVisible['toString']()));}else userPrefersDescriptionVisible=isDesktop();lightboxEl[_0x164fb7(0x166)]('click',_0x54fd30=>{const _0x87eb48=_0x164fb7,_0x4ba00a=_0x54fd30['target'],_0x95db52=_0x4ba00a['closest']('.lightbox-media-wrapper'),_0x1e1da4=_0x4ba00a['closest']('.lightbox-desc-card'),_0x443454=_0x4ba00a[_0x87eb48(0x202)](_0x87eb48(0x190)),_0x19e880=_0x4ba00a['closest']('.lightbox-description-overlay'),_0x242792=_0x4ba00a['closest']('#lightbox-content'),_0x4273e0=_0x4ba00a['closest']('#lightbox-inner-wrapper'),_0x3a9461=_0x95db52||_0x1e1da4||_0x443454||_0x19e880;(_0x4ba00a===lightboxEl||_0x242792&&!_0x3a9461||_0x4273e0&&!_0x3a9461)&&!_0x3a9461&&_closeLightbox();}),document['addEventListener']('keydown',_0x3d2db2=>{const _0xa81104=_0x164fb7;'Escape'===_0x3d2db2[_0xa81104(0x172)]&&lightboxEl&&_0xa81104(0x208)===lightboxEl[_0xa81104(0x18a)][_0xa81104(0x17b)]&&_closeLightbox();}),lightboxContentEl&&(lightboxContentEl['addEventListener'](_0x164fb7(0x193),handleTouchStart,{'passive':!0x0}),lightboxContentEl[_0x164fb7(0x166)](_0x164fb7(0x182),handleTouchMove,{'passive':!0x0}),lightboxContentEl['addEventListener'](_0x164fb7(0x138),handleTouchEnd,{'passive':!0x0}));}else console['error']('Lightbox\x20DOM\x20elements\x20not\x20found\x20during\x20init!\x20Ensure\x20HTML\x20is\x20loaded.');},'open':(_0x46656a,_0x5036b0=0x0)=>{const _0x8c8a4b=_0x5912c6;lightboxEl?(closeLightboxTimeoutId&&(clearTimeout(closeLightboxTimeoutId),closeLightboxTimeoutId=null),onLightboxCloseTransitionEnd&&(lightboxEl[_0x8c8a4b(0x14e)]('transitionend',onLightboxCloseTransitionEnd),onLightboxCloseTransitionEnd=null),lightboxOpenGeneration++,lightboxEl['classList']['remove'](_0x8c8a4b(0x13e)),lightboxEl['style'][_0x8c8a4b(0x177)]='',lightboxEl['style'][_0x8c8a4b(0x1c1)]=_0x8c8a4b(0x198),lightboxContentEl&&(lightboxContentEl['style'][_0x8c8a4b(0x177)]=''),_openLightboxByProjectAndImage(_0x46656a,_0x5036b0,0x0,!0x1),lightboxEl[_0x8c8a4b(0x18a)][_0x8c8a4b(0x17b)]=_0x8c8a4b(0x208),lightboxEl['style']['visibility']='visible',lightboxEl['offsetWidth'],requestAnimationFrame(()=>{const _0x35d882=_0x8c8a4b;lightboxEl[_0x35d882(0x1cf)][_0x35d882(0x170)](_0x35d882(0x16d));}),document['body']['style'][_0x8c8a4b(0x173)]=_0x8c8a4b(0x198),sendMessageToParent({'type':_0x8c8a4b(0x1f6),'enabled':!0x0})):console['error'](_0x8c8a4b(0x13a));},'navigateNext':()=>{isNavigationThrottled||(_navigateNext(),throttleNavigationButtons());},'navigatePrevious':()=>{isNavigationThrottled||(_navigatePrevious(),throttleNavigationButtons());},'close':()=>_closeLightbox(),'toggleDescription':()=>{const _0x4d636f=_0x5912c6;if(!lightboxContentEl)return;const _0x3819c6=lightboxContentEl['querySelector'](_0x4d636f(0x137));if(_0x3819c6){if(isDesktop())toggleDesktopDescriptionOverlay(_0x3819c6);else toggleMobileOverlays((allProjectPostsData[currentProjectIndex]||{})['title'],'toggle');}},'isOpen':()=>lightboxEl&&'flex'===lightboxEl['style'][_0x5912c6(0x17b)]};