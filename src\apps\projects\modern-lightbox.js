/*
 * modern-lightbox.js - Modern Lightbox Controller
 * Simplified lightbox system using the Universal Media Container
 * Replaces the complex legacy lightbox with a clean, maintainable solution
 */

/**
 * Modern Lightbox State Management
 */
class LightboxState {
  constructor() {
    this.state = {
      isOpen: false,
      currentProject: null,
      currentMediaIndex: 0,
      mediaItems: [],
      isTransitioning: false,
      showDescription: true
    };
    this.listeners = new Set();
  }

  setState(newState) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...newState };
    this.notifyListeners(oldState, this.state);
  }

  getState() {
    return { ...this.state };
  }

  subscribe(listener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  notifyListeners(oldState, newState) {
    this.listeners.forEach(listener => {
      try {
        listener(newState, oldState);
      } catch (error) {
        console.error('State listener error:', error);
      }
    });
  }
}

/**
 * Modern Lightbox Controller
 * Simplified lightbox implementation using Universal Media Container
 */
class ModernLightbox {
  constructor(options = {}) {
    this.options = {
      containerSelector: '#project-lightbox',
      mediaContainerSelector: '.universal-media-container',
      closeOnBackdropClick: true,
      enableKeyboardNavigation: true,
      showNavigationControls: true,
      animationDuration: 300,
      ...options
    };

    this.state = new LightboxState();
    this.container = null;
    this.mediaContainer = null;
    this.controls = null;
    this.projectsData = [];
    
    this.init();
  }

  /**
   * Initialize the lightbox
   */
  init() {
    this.setupDOM();
    this.setupEventListeners();
    this.setupStateSubscription();
  }

  /**
   * Setup DOM elements and structure
   */
  setupDOM() {
    // Get or create main container
    this.container = document.querySelector(this.options.containerSelector);
    if (!this.container) {
      this.container = this.createLightboxContainer();
      document.body.appendChild(this.container);
    }

    // Setup container structure
    this.container.innerHTML = `
      <div class="lightbox-backdrop"></div>
      <div class="lightbox-content">
        <div class="universal-media-container" tabindex="0"></div>
        <div class="lightbox-controls">
          <button class="control-btn close-btn" aria-label="Close lightbox">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <button class="control-btn prev-btn" aria-label="Previous media">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="15,18 9,12 15,6"></polyline>
            </svg>
          </button>
          <button class="control-btn next-btn" aria-label="Next media">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,18 15,12 9,6"></polyline>
            </svg>
          </button>
          <button class="control-btn info-btn" aria-label="Toggle project info">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          </button>
        </div>
        <div class="lightbox-info">
          <div class="project-title"></div>
          <div class="project-description"></div>
          <div class="media-counter"></div>
        </div>
      </div>
    `;

    // Initialize Universal Media Container
    const mediaContainerElement = this.container.querySelector('.universal-media-container');
    this.mediaContainer = new UniversalMediaContainer(mediaContainerElement);

    // Get control elements
    this.controls = {
      close: this.container.querySelector('.close-btn'),
      prev: this.container.querySelector('.prev-btn'),
      next: this.container.querySelector('.next-btn'),
      info: this.container.querySelector('.info-btn'),
      backdrop: this.container.querySelector('.lightbox-backdrop')
    };

    // Get info elements
    this.infoElements = {
      container: this.container.querySelector('.lightbox-info'),
      title: this.container.querySelector('.project-title'),
      description: this.container.querySelector('.project-description'),
      counter: this.container.querySelector('.media-counter')
    };
  }

  /**
   * Create the main lightbox container
   */
  createLightboxContainer() {
    const container = document.createElement('div');
    container.id = 'project-lightbox';
    container.className = 'modern-lightbox';
    container.setAttribute('role', 'dialog');
    container.setAttribute('aria-modal', 'true');
    container.setAttribute('aria-hidden', 'true');
    return container;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Control button events
    this.controls.close.addEventListener('click', () => this.close());
    this.controls.prev.addEventListener('click', () => this.navigatePrevious());
    this.controls.next.addEventListener('click', () => this.navigateNext());
    this.controls.info.addEventListener('click', () => this.toggleInfo());

    // Backdrop click to close
    if (this.options.closeOnBackdropClick) {
      this.controls.backdrop.addEventListener('click', () => this.close());
    }

    // Keyboard navigation
    if (this.options.enableKeyboardNavigation) {
      document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    // Media container events
    this.mediaContainer.container.addEventListener('media-container-media-loaded', (e) => {
      this.onMediaLoaded(e.detail);
    });

    this.mediaContainer.container.addEventListener('media-container-media-error', (e) => {
      this.onMediaError(e.detail);
    });

    this.mediaContainer.container.addEventListener('media-container-escape-pressed', () => {
      this.close();
    });

    this.mediaContainer.container.addEventListener('media-container-navigate-previous', () => {
      this.navigatePrevious();
    });

    this.mediaContainer.container.addEventListener('media-container-navigate-next', () => {
      this.navigateNext();
    });
  }

  /**
   * Setup state subscription for UI updates
   */
  setupStateSubscription() {
    this.state.subscribe((newState, oldState) => {
      this.updateUI(newState, oldState);
    });
  }

  /**
   * Initialize with projects data
   */
  initWithData(projectsData) {
    this.projectsData = projectsData || [];
  }

  /**
   * Open lightbox with specific project and media index
   */
  async open(projectIndex, mediaIndex = 0) {
    try {
      const project = this.projectsData[projectIndex];
      if (!project) {
        throw new Error(`Project not found at index ${projectIndex}`);
      }

      // Parse media items from project data
      const mediaItems = this.parseProjectMedia(project);
      if (mediaItems.length === 0) {
        throw new Error('No media items found in project');
      }

      // Validate media index
      const validMediaIndex = Math.max(0, Math.min(mediaIndex, mediaItems.length - 1));

      // Update state
      this.state.setState({
        isOpen: true,
        currentProject: project,
        currentMediaIndex: validMediaIndex,
        mediaItems: mediaItems,
        isTransitioning: true
      });

      // Show lightbox
      this.container.classList.add('opening');
      this.container.setAttribute('aria-hidden', 'false');
      
      // Focus the media container for keyboard navigation
      this.mediaContainer.container.focus();

      // Load the media
      await this.loadCurrentMedia();

      // Complete opening transition
      setTimeout(() => {
        this.container.classList.remove('opening');
        this.container.classList.add('open');
        this.state.setState({ isTransitioning: false });
      }, this.options.animationDuration);

    } catch (error) {
      console.error('Failed to open lightbox:', error);
      this.handleError(error);
    }
  }

  /**
   * Close the lightbox
   */
  close() {
    if (!this.state.getState().isOpen) return;

    this.state.setState({ isTransitioning: true });
    
    this.container.classList.add('closing');
    this.container.classList.remove('open');

    setTimeout(() => {
      this.container.classList.remove('closing');
      this.container.setAttribute('aria-hidden', 'true');
      
      this.state.setState({
        isOpen: false,
        currentProject: null,
        currentMediaIndex: 0,
        mediaItems: [],
        isTransitioning: false
      });

      // Clear media container
      this.mediaContainer.clearContent();
    }, this.options.animationDuration);
  }

  /**
   * Navigate to previous media item
   */
  async navigatePrevious() {
    const state = this.state.getState();
    if (state.isTransitioning || state.mediaItems.length <= 1) return;

    const newIndex = state.currentMediaIndex > 0 
      ? state.currentMediaIndex - 1 
      : state.mediaItems.length - 1;

    await this.navigateToMedia(newIndex);
  }

  /**
   * Navigate to next media item
   */
  async navigateNext() {
    const state = this.state.getState();
    if (state.isTransitioning || state.mediaItems.length <= 1) return;

    const newIndex = state.currentMediaIndex < state.mediaItems.length - 1
      ? state.currentMediaIndex + 1
      : 0;

    await this.navigateToMedia(newIndex);
  }

  /**
   * Navigate to specific media index
   */
  async navigateToMedia(mediaIndex) {
    const state = this.state.getState();
    if (mediaIndex === state.currentMediaIndex || state.isTransitioning) return;

    this.state.setState({
      isTransitioning: true,
      currentMediaIndex: mediaIndex
    });

    try {
      await this.loadCurrentMedia();
    } catch (error) {
      console.error('Navigation error:', error);
      this.handleError(error);
    } finally {
      this.state.setState({ isTransitioning: false });
    }
  }

  /**
   * Toggle project info visibility
   */
  toggleInfo() {
    const state = this.state.getState();
    this.state.setState({
      showDescription: !state.showDescription
    });
  }

  /**
   * Load current media item
   */
  async loadCurrentMedia() {
    const state = this.state.getState();
    const mediaItem = state.mediaItems[state.currentMediaIndex];

    if (!mediaItem) {
      throw new Error('No media item found at current index');
    }

    // Transform media item to Universal Media Container format
    const mediaData = {
      type: mediaItem.type || 'image',
      src: mediaItem.src,
      options: {
        poster: mediaItem.poster,
        posterMobile: mediaItem.posterMobile,
        alt: state.currentProject?.title || 'Project media',
        autoplay: mediaItem.type === 'video',
        loop: mediaItem.type === 'video',
        muted: true
      }
    };

    await this.mediaContainer.loadMedia(mediaData);
  }

  /**
   * Parse project media from project data
   */
  parseProjectMedia(project) {
    const mediaItems = [];

    try {
      // Parse images array if it exists
      let projectImages = [];
      if (project.images) {
        if (typeof project.images === 'string') {
          projectImages = JSON.parse(project.images);
        } else if (Array.isArray(project.images)) {
          projectImages = project.images;
        }
      }

      // Process parsed images
      if (Array.isArray(projectImages) && projectImages.length > 0) {
        projectImages.forEach(item => {
          if (typeof item === 'string') {
            // Legacy format: array of strings
            mediaItems.push({
              type: 'image',
              src: this.toAbsoluteAssetPath(item)
            });
          } else if (typeof item === 'object' && item.src) {
            // New format: array of objects
            mediaItems.push({
              type: item.type || 'image',
              src: this.toAbsoluteAssetPath(item.src),
              poster: item.poster ? this.toAbsoluteAssetPath(item.poster) : null,
              posterMobile: item.posterMobile ? this.toAbsoluteAssetPath(item.posterMobile) : null
            });
          }
        });
      }

      // Fallback to main project source if no images processed
      if (mediaItems.length === 0 && project.src) {
        mediaItems.push({
          type: project.type || 'image',
          src: this.toAbsoluteAssetPath(project.src),
          poster: project.poster ? this.toAbsoluteAssetPath(project.poster) : null,
          posterMobile: project.posterMobile ? this.toAbsoluteAssetPath(project.posterMobile) : null
        });
      }

    } catch (error) {
      console.error('Error parsing project media:', error);

      // Fallback to main project source
      if (project.src) {
        mediaItems.push({
          type: project.type || 'image',
          src: this.toAbsoluteAssetPath(project.src),
          poster: project.poster ? this.toAbsoluteAssetPath(project.poster) : null
        });
      }
    }

    return mediaItems;
  }

  /**
   * Convert relative asset path to absolute
   */
  toAbsoluteAssetPath(path) {
    if (!path) return '';
    if (path.startsWith('http') || path.startsWith('/')) return path;
    return `/${path}`;
  }

  /**
   * Handle keyboard events
   */
  handleKeyboard(event) {
    if (!this.state.getState().isOpen) return;

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        this.close();
        break;

      case 'ArrowLeft':
        event.preventDefault();
        this.navigatePrevious();
        break;

      case 'ArrowRight':
        event.preventDefault();
        this.navigateNext();
        break;

      case 'i':
      case 'I':
        event.preventDefault();
        this.toggleInfo();
        break;
    }
  }

  /**
   * Update UI based on state changes
   */
  updateUI(newState, oldState) {
    // Update navigation controls
    this.updateNavigationControls(newState);

    // Update project info
    this.updateProjectInfo(newState);

    // Update media counter
    this.updateMediaCounter(newState);

    // Update info visibility
    this.updateInfoVisibility(newState);
  }

  /**
   * Update navigation control states
   */
  updateNavigationControls(state) {
    const hasMultipleItems = state.mediaItems.length > 1;

    this.controls.prev.style.display = hasMultipleItems ? 'block' : 'none';
    this.controls.next.style.display = hasMultipleItems ? 'block' : 'none';

    this.controls.prev.disabled = state.isTransitioning;
    this.controls.next.disabled = state.isTransitioning;
    this.controls.close.disabled = state.isTransitioning;
  }

  /**
   * Update project information display
   */
  updateProjectInfo(state) {
    if (!state.currentProject) return;

    this.infoElements.title.textContent = state.currentProject.title || '';
    this.infoElements.description.textContent = state.currentProject.description || '';
  }

  /**
   * Update media counter display
   */
  updateMediaCounter(state) {
    if (state.mediaItems.length > 1) {
      this.infoElements.counter.textContent =
        `${state.currentMediaIndex + 1} / ${state.mediaItems.length}`;
      this.infoElements.counter.style.display = 'block';
    } else {
      this.infoElements.counter.style.display = 'none';
    }
  }

  /**
   * Update info panel visibility
   */
  updateInfoVisibility(state) {
    this.infoElements.container.classList.toggle('visible', state.showDescription);
    this.controls.info.classList.toggle('active', state.showDescription);
  }

  /**
   * Handle media loaded event
   */
  onMediaLoaded(detail) {
    // Media successfully loaded
    console.log('Media loaded:', detail);
  }

  /**
   * Handle media error event
   */
  onMediaError(detail) {
    console.error('Media error:', detail);
    this.handleError(detail.error);
  }

  /**
   * Handle general errors
   */
  handleError(error) {
    console.error('Lightbox error:', error);
    // Could show error notification to user here
  }

  /**
   * Get current state
   */
  getState() {
    return this.state.getState();
  }

  /**
   * Destroy lightbox and clean up
   */
  destroy() {
    this.close();

    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyboard);

    // Destroy media container
    if (this.mediaContainer) {
      this.mediaContainer.destroy();
    }

    // Remove DOM elements
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    // Clear references
    this.container = null;
    this.mediaContainer = null;
    this.controls = null;
    this.projectsData = [];
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ModernLightbox;
} else if (typeof window !== 'undefined') {
  window.ModernLightbox = ModernLightbox;
}
