{"name": "@eslint/css", "version": "0.8.1", "description": "CSS linting plugin for ESLint", "author": "<PERSON>", "type": "module", "main": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}}, "./syntax": {"require": {"types": "./dist/cjs/syntax/index.d.cts", "default": "./dist/cjs/syntax/index.cjs"}, "import": {"types": "./dist/esm/syntax/index.d.ts", "default": "./dist/esm/syntax/index.js"}}, "./types": {"require": {"types": "./dist/cjs/types.cts"}, "import": {"types": "./dist/esm/types.d.ts"}}}, "files": ["dist"], "publishConfig": {"access": "public"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "README.md": ["npm run build:update-rules-docs"], "!(*.js)": "prettier --write --ignore-unknown", "{src/rules/*.js,tools/update-rules-docs.js}": ["node tools/update-rules-docs.js", "git add README.md"]}, "repository": {"type": "git", "url": "git+https://github.com/eslint/css.git"}, "bugs": {"url": "https://github.com/eslint/css/issues"}, "homepage": "https://github.com/eslint/css#readme", "scripts": {"build:dedupe-types": "node tools/dedupe-types.js dist/cjs/index.cjs dist/esm/index.js", "build:cts": "node -e \"fs.copyFileSync('dist/esm/index.d.ts', 'dist/cjs/index.d.cts')\"", "build:syntax-cts": "node -e \"fs.copyFileSync('dist/esm/syntax/index.d.ts', 'dist/cjs/syntax/index.d.cts')\" && node tools/update-cts.js dist/cjs/types.cts dist/cjs/index.d.cts", "build": "rollup -c && npm run build:dedupe-types && tsc -p tsconfig.esm.json && npm run build:cts && tsc -p tsconfig.syntax.json && npm run build:syntax-cts", "build:readme": "node tools/update-readme.js", "build:update-rules-docs": "node tools/update-rules-docs.js", "build:baseline": "node tools/generate-baseline.js", "test:jsr": "npx jsr@latest publish --dry-run", "pretest": "npm run build", "lint": "eslint", "fmt": "prettier --write .", "fmt:check": "prettier --check .", "test": "mocha tests/**/*.js", "test:coverage": "c8 npm test", "test:types": "npm run build && tsc -p tests/types/tsconfig.json"}, "keywords": ["eslint", "eslint-plugin", "eslintplugin", "css", "linting"], "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.14.0", "@eslint/css-tree": "^3.3.3", "@eslint/plugin-kit": "^0.3.1"}, "devDependencies": {"@eslint/json": "^0.5.0", "c8": "^9.1.0", "compute-baseline": "^0.3.1", "dedent": "^1.5.3", "eslint": "^9.24.0", "eslint-config-eslint": "^11.0.0", "eslint-plugin-eslint-plugin": "^6.3.2", "got": "^14.4.2", "lint-staged": "^15.2.7", "mdast-util-from-markdown": "^2.0.2", "mdn-data": "^2.21.0", "mocha": "^10.4.0", "prettier": "^3.4.1", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "typescript": "^5.8.2", "web-features": "^2.35.1", "yorkie": "^2.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}