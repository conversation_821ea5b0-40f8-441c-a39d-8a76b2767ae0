body,html{margin:0;padding:0;width:100%;height:100%;overflow:hidden;background-color:#212121;position:relative}img{width:20px;height:20px}.music-player,.texture{position:absolute;border-radius:0}.music-player{left:50%;top:50%;transform:translate(-50%,-50%) scale(.6);transform-origin:center center;display:flex;justify-content:center;align-items:center;width:697px;height:372px;border:2px solid #1a1a1a;background:linear-gradient(380deg,#1a1a1a 0,#3c3c3c 100%)}.texture{z-index:1;width:100%;height:100%;background-clip:border-box;mix-blend-mode:multiply;opacity:.25;pointer-events:none}.mp-inner{display:flex;justify-content:space-around;align-items:center;width:98%;height:96%;border-radius:0;background:linear-gradient(180deg,#1b1b1b 0,#212121 11.2%,#3a3a3a 100%)}.album-artwork{position:relative;z-index:2;width:345px;height:313px;border-radius:28px;border:4px solid #1e1e1e;background-size:100%;transition:background-image .2s ease-in-out}.album-border{display:flex;flex-direction:column;justify-content:space-between;width:100%;height:100%;filter:blur(1px)}.album-reflection{position:absolute;width:30%;height:100%;opacity:.4;transform:translateX(128px) skewX(-30deg);filter:blur(2.4px);background:linear-gradient(180deg,rgba(255,255,255,.2)0,rgba(255,255,255,0) 100%)}.albmu-scrn-fx{position:absolute;display:flex;gap:2px;border-radius:30px;width:100%;height:100%;overflow:hidden}.scrn-fx-line{width:4px;height:100%;background:linear-gradient(180deg,rgba(2,2,2,.1)0,rgba(2,2,2,0) 100%);filter:blur(.5px)}.album-text{position:absolute;width:100%;height:70%;background:linear-gradient(180deg,rgba(2,2,2,0) 30%,rgba(2,2,2,.671) 60%,rgba(2,2,2,.8) 80%,rgba(2,2,2,.98) 100%);bottom:0;filter:blur(.6px);opacity:.89;border-radius:0 0 24px 24px;display:flex;flex-direction:column;justify-content:flex-end;padding:10px 20px 20px;box-sizing:border-box;color:#fff;font-family:Tahoma,Arial,sans-serif}.album-text h1{font-size:30px;margin:0 0 4px;text-shadow:0 2px 2px rgba(0,0,0,.5)}.album-text p{font-size:24px;margin:0;text-shadow:0 2px 4px rgba(0,0,0,.4)}.button-controls{display:flex;justify-content:center;align-items:center;width:254px;height:254px;margin-right:10px;background:linear-gradient(180deg,#292929 0,#1f1f1f 100%);border:4px solid #0d0d0d;border-radius:50%;perspective:120em}.btn-overlay{position:relative;width:236px;height:236px;border-radius:50%;background:linear-gradient(175deg,#1f1f1f 3.94%,#2d2d2d 44.59%,#3d3d3d 81.99%);box-shadow:0 4px 7px 1px rgba(255,255,255,.11) inset;transition:transform .1s ease,box-shadow .1s ease,filter .1s ease}.btn-overlay.left{transform:rotateY(-6deg) skew(1deg,-1deg);box-shadow:0 4px 7px 1px rgba(255,255,255,.11) inset,16px 4px 14px 0 rgba(0,0,0,.05) inset;filter:drop-shadow(0 4px 4px rgba(0,0,0,.25))}.btn-overlay.right{transform:rotateY(6deg) skew(-1deg,1deg);box-shadow:0 4px 7px 1px rgba(255,255,255,.11) inset,-16px 4px 14px 0 rgba(0,0,0,.05) inset;filter:drop-shadow(0 4px -4px rgba(0,0,0,.25))}.btn-overlay.down,.btn-overlay.up{transform:rotateX(8deg);box-shadow:0 4px 7px 1px rgba(255,255,255,.09) inset,0-4px 14px 0 rgba(0,0,0,.05) inset;filter:drop-shadow(0 4px -4px rgba(0,0,0,.25))}.btn-overlay.down{transform:rotateX(-8deg);box-shadow:0 4px 7px 1px rgba(255,255,255,.1) inset,0-14px 14px 0 rgba(0,0,0,.05) inset}.play-btn-shadow{width:108px;height:108px;background:rgba(0,0,0,.7);filter:blur(1.5px)}.play-btn,.play-btn-overlay,.play-btn-shadow{position:absolute;border-radius:50%;top:50%;left:50%;transform:translate(-50%,-50%)}.play-btn{cursor:pointer;width:100px;height:100px;background:linear-gradient(180deg,#292929 0,#1f1f1f 100%);transition:transform .1s ease}.play-btn.pressed{transform:translate(-50%,-50%) scale(.96)}.play-btn-overlay{width:86px;height:86px;background:linear-gradient(189deg,#1f1f1f 6.91%,#2d2d2d 50.27%,#393939 83.66%);box-shadow:0 1px 2px 0 rgba(255,255,255,.12) inset}.play-icon,.skip-left,.skip-right,.vol-down,.vol-up{position:absolute;display:flex;align-items:center;justify-content:center}.play-icon{width:40px;height:40px;border-radius:50%;top:50%;left:50%;transform:translate(-50%,-50%)}.skip-left,.skip-right,.vol-down,.vol-up{cursor:pointer;transition:filter .1s ease}.skip-left,.skip-right{width:40px;height:25px;top:49%}.skip-left{left:5%;transform:translateY(-50%) rotate(-2deg)}.skip-right{right:5%;transform:translateY(-50%) rotate(2deg)}.vol-up{width:30px;height:34px;top:8%;left:50.5%;transform:translateX(-50%) rotate(1deg)}.vol-down{width:28px;height:24px;bottom:5%;left:50%;transform:translateX(-50%) rotate(-1deg)}.play-icon svg,.skip-left svg,.skip-right svg,.vol-down svg,.vol-up svg{width:100%;height:100%;display:block}