[{"type": "video", "src": "assets/apps/projects/event-work/aac-thumb.mp4", "poster": "assets/apps/projects/event-work/aac-poster.webp", "images": [{"src": "assets/apps/projects/event-work/aac.mp4", "poster": "assets/apps/projects/event-work/aac-poster.webp"}, {"src": "assets/apps/projects/event-work/jason-leonard.mp4", "poster": "assets/apps/projects/event-work/jason-leonard-poster.webp"}], "title": "Event Work Reel", "subtitle": "Commissioned videos for live events and stage intros.", "description": "Produced a series of event videos across multiple commissions, including player introductions, highlight reels, and background visuals. Each video was built for large-screen projection and tailored to match the pacing, tone, and technical setup of the event. Featured speakers included professional rugby players.", "bulletPoints": ["<PERSON> (ENG)", "<PERSON> (AUS)"], "toolsUsed": ["Premiere Pro", "After Effects"], "workType": "client", "alt": "Event Work Reel"}, {"type": "image", "src": "assets/apps/projects/game-day/mavs-final.webp", "images": ["assets/apps/projects/game-day/abs-final.webp", "assets/apps/projects/game-day/blues-xv.webp"], "title": "Game Day Graphics", "subtitle": "Matchday visuals including lineups, gameday tiles, and fulltime results.", "description": "Created a set of individual athlete graphics as personal design studies. Each piece explored different approaches to tone, lighting, and layout without using templates or team branding. Focused on building clean, high-impact visuals through image manipulation and composition.", "bulletPoints": ["Consistent framework tailored for pre and post-game use", "Emphasis on clarity, team identity, and match context", "Flexible layouts suited to a range of content types"], "toolsUsed": ["Photoshop, Illustrator"], "alt": "Game Day Graphics"}, {"type": "image", "src": "assets/apps/projects/marky/marky-thumb.webp", "images": ["assets/apps/projects/marky/marky1.webp", "assets/apps/projects/marky/marky2.webp", "assets/apps/projects/marky/marky3.webp", "assets/apps/projects/marky/marky4.webp", "assets/apps/projects/marky/marky5.webp"], "title": "Video Clipper", "subtitle": "Rapid-fire video clipping app built to streamline video production workflow.", "description": "A custom desktop app built for clipping video with speed and precision. Marky lets you import or download footage, scrub cleanly, mark in and out points, and export clips fast. Designed with a minimal UI and built for real workflows using local tools and a focused feature set.", "bulletPoints": ["Imports video or downloads via URL", "Frame-accurate timeline scrubbing", "Fast clip export using ffmpeg under the hood"], "toolsUsed": ["VS Code, Generative AI"], "alt": "Video Clipper"}, {"type": "image", "src": "assets/apps/projects/hero-graphics/patrickmahomes.webp", "images": ["assets/apps/projects/hero-graphics/minnesotas-coldest.webp"], "title": "Hero Graphics", "subtitle": "Stylised athlete edits and high-impact Photoshop visuals.", "description": "A collection of image-based design work built through Photoshop. This project explores compositing, retouching, and visual manipulation as tools to push style, technique, and creative direction across different subjects and formats.", "bulletPoints": ["Designed to highlight individuality and presence", "Balances composition, contrast, and scale", "Adapts style to suit each player and moment"], "toolsUsed": ["Photoshop"], "alt": "Hero Graphics"}, {"type": "image", "src": "assets/apps/projects/mitchivinxp/image1.webp", "images": ["assets/apps/projects/mitchivinxp/image1.webp", "assets/apps/projects/mitchivinxp/image2.webp", "assets/apps/projects/mitchivinxp/image3.webp", "assets/apps/projects/mitchivinxp/image4.webp", "assets/apps/projects/mitchivinxp/image5.webp", "assets/apps/projects/mitchivinxp/image6.webp"], "title": "<PERSON><PERSON><PERSON>", "subtitle": "A fully interactive Windows XP simulation reimagined as a design portfolio experience.", "description": "A fully interactive portfolio built inside a custom Windows XP simulation. Made to feel like a working OS, it includes functional apps, retro UI, and detailed screens built from scratch. Every part, from the boot screen to the desktop, was designed to show execution, control, and originality.", "bulletPoints": ["Fully navigable desktop with mobile support", "Built from scratch using HTML, CSS, and JavaScript", "Focused on detail, interface design, and creative direction"], "toolsUsed": ["HTML/CSS/JS, Generative AI, Illustrator, Photoshop"], "alt": "<PERSON><PERSON><PERSON>"}]