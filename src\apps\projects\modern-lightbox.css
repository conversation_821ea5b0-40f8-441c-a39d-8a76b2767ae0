/*
 * modern-lightbox.css - Modern Lightbox Styles
 * Clean, flexible lightbox design without aspect ratio constraints
 * Uses modern CSS features for responsive behavior
 */

/* ===== Modern Lightbox Base Styles ===== */
.modern-lightbox {
  /* Full viewport overlay */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100000;
  
  /* Initial hidden state */
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  
  /* Smooth transitions */
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Flexbox layout */
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* Typography */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #fff;
}

/* ===== Lightbox States ===== */
.modern-lightbox.opening,
.modern-lightbox.open {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.modern-lightbox.closing {
  opacity: 0;
  visibility: visible;
  pointer-events: none;
}

/* ===== Backdrop ===== */
.lightbox-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* ===== Main Content Area ===== */
.lightbox-content {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 95vw;
  max-height: 95vh;
  display: grid;
  grid-template-areas: 
    "media"
    "info";
  grid-template-rows: 1fr auto;
  gap: 1rem;
  padding: 2rem;
  box-sizing: border-box;
}

/* ===== Universal Media Container in Lightbox ===== */
.lightbox-content .universal-media-container {
  grid-area: media;
  width: 100%;
  height: 100%;
  min-height: 0; /* Important for grid item */
  
  /* Remove default background for lightbox context */
  background: transparent;
  
  /* Focus styles */
  outline: none;
}

.lightbox-content .universal-media-container:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 4px;
}

/* ===== Controls ===== */
.lightbox-controls {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.control-btn {
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-btn svg {
  width: 20px;
  height: 20px;
}

/* Navigation controls positioning */
.prev-btn,
.next-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
}

.prev-btn {
  left: 2rem;
}

.next-btn {
  right: 2rem;
}

.prev-btn svg,
.next-btn svg {
  width: 24px;
  height: 24px;
}

/* Info button active state */
.info-btn.active {
  background: rgba(255, 255, 255, 0.2);
}

/* ===== Project Information ===== */
.lightbox-info {
  grid-area: info;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px;
  padding: 1.5rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.lightbox-info.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #fff;
}

.project-description {
  font-size: 1rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
}

.media-counter {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* ===== Responsive Design ===== */

/* Tablet and smaller */
@media (max-width: 1024px) {
  .lightbox-content {
    padding: 1.5rem;
    gap: 0.75rem;
  }
  
  .lightbox-controls {
    top: 1.5rem;
    right: 1.5rem;
  }
  
  .prev-btn,
  .next-btn {
    width: 50px;
    height: 50px;
  }
  
  .prev-btn {
    left: 1.5rem;
  }
  
  .next-btn {
    right: 1.5rem;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .lightbox-content {
    padding: 1rem;
    max-width: 100vw;
    max-height: 100vh;
    grid-template-areas: 
      "media"
      "info";
    grid-template-rows: 1fr auto;
  }
  
  .lightbox-controls {
    top: 1rem;
    right: 1rem;
    gap: 0.25rem;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
  }
  
  .control-btn svg {
    width: 18px;
    height: 18px;
  }
  
  /* Move navigation to bottom on mobile */
  .prev-btn,
  .next-btn {
    position: static;
    transform: none;
    width: 40px;
    height: 40px;
  }
  
  .prev-btn svg,
  .next-btn svg {
    width: 18px;
    height: 18px;
  }
  
  .project-title {
    font-size: 1.25rem;
  }
  
  .project-description {
    font-size: 0.9rem;
  }
  
  .lightbox-info {
    padding: 1rem;
    border-radius: 8px;
  }
}

/* Small mobile */
@media (max-width: 480px) {
  .lightbox-content {
    padding: 0.5rem;
  }
  
  .lightbox-controls {
    top: 0.5rem;
    right: 0.5rem;
  }
  
  .lightbox-info {
    padding: 0.75rem;
  }
  
  .project-title {
    font-size: 1.1rem;
  }
  
  .project-description {
    font-size: 0.85rem;
  }
}

/* ===== Accessibility Enhancements ===== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .lightbox-backdrop {
    background: rgba(0, 0, 0, 0.95);
  }
  
  .control-btn {
    background: #000;
    border: 2px solid #fff;
  }
  
  .lightbox-info {
    background: #000;
    border: 1px solid #fff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .modern-lightbox,
  .control-btn,
  .lightbox-info {
    transition: none;
  }
  
  .control-btn:hover {
    transform: none;
  }
  
  .lightbox-backdrop {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .lightbox-info {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* ===== Print Styles ===== */
@media print {
  .modern-lightbox {
    display: none !important;
  }
}
