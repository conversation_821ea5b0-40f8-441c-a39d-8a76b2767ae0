/*
 * universal-media-container.js - Universal Media Container Component
 * A flexible, reusable media container that can handle any media type
 * without aspect ratio constraints or complex configuration.
 */

/**
 * Universal Media Container Class
 * Handles dynamic loading and display of various media types (images, videos, etc.)
 * with flexible sizing and responsive behavior.
 */
class UniversalMediaContainer {
  constructor(containerElement) {
    this.container = containerElement;
    this.mediaContent = null;
    this.overlay = null;
    this.currentMedia = null;
    this.mediaCache = new Map();
    this.isLoading = false;
    
    this.init();
  }

  /**
   * Initialize the container structure and event listeners
   */
  init() {
    // Ensure container has proper structure
    this.setupContainerStructure();
    
    // Add base classes and attributes
    this.container.classList.add('universal-media-container');
    this.container.setAttribute('data-media-type', 'none');
    
    // Setup event listeners
    this.setupEventListeners();
  }

  /**
   * Setup the internal DOM structure
   */
  setupContainerStructure() {
    // Clear existing content
    this.container.innerHTML = '';
    
    // Create media content area
    this.mediaContent = document.createElement('div');
    this.mediaContent.className = 'media-content';
    
    // Create overlay area for controls/info
    this.overlay = document.createElement('div');
    this.overlay.className = 'media-overlay';
    
    // Append to container
    this.container.appendChild(this.mediaContent);
    this.container.appendChild(this.overlay);
  }

  /**
   * Setup event listeners for the container
   */
  setupEventListeners() {
    // Handle container clicks (for closing, etc.)
    this.container.addEventListener('click', (e) => {
      if (e.target === this.container) {
        this.dispatchEvent('container-click', { originalEvent: e });
      }
    });

    // Handle keyboard events
    this.container.addEventListener('keydown', (e) => {
      this.handleKeyboardNavigation(e);
    });
  }

  /**
   * Load media dynamically based on type
   * @param {Object} mediaData - Media information object
   * @param {string} mediaData.type - 'image' | 'video' | 'iframe'
   * @param {string} mediaData.src - Media source URL
   * @param {Object} mediaData.options - Additional options
   */
  async loadMedia(mediaData) {
    try {
      // Validate input
      if (!mediaData || !mediaData.src) {
        throw new Error('Invalid media data provided');
      }

      // Show loading state
      this.setLoadingState(true);
      
      // Clear previous content
      this.clearContent();
      
      // Create media element based on type
      const mediaElement = await this.createMediaElement(mediaData);
      
      // Apply universal styling and attributes
      this.applyMediaStyling(mediaElement, mediaData);
      
      // Insert into container
      this.mediaContent.appendChild(mediaElement);
      
      // Update container state
      this.updateContainerState(mediaData);
      
      // Wait for media to load
      await this.waitForMediaLoad(mediaElement);
      
      // Hide loading state and show content
      this.setLoadingState(false);
      this.showContent(mediaElement);
      
      // Store reference
      this.currentMedia = mediaElement;
      
      // Cache the media data
      this.mediaCache.set(mediaData.src, mediaData);
      
      // Dispatch loaded event
      this.dispatchEvent('media-loaded', { 
        mediaData, 
        element: mediaElement,
        dimensions: this.getMediaDimensions()
      });
      
    } catch (error) {
      this.handleLoadError(error, mediaData);
    }
  }

  /**
   * Create appropriate media element based on type
   */
  async createMediaElement(mediaData) {
    const { type, src, options = {} } = mediaData;
    
    switch (type.toLowerCase()) {
      case 'image':
        return this.createImageElement(src, options);
      
      case 'video':
        return this.createVideoElement(src, options);
      
      case 'iframe':
        return this.createIframeElement(src, options);
      
      default:
        // Default to image for unknown types
        console.warn(`Unknown media type: ${type}, defaulting to image`);
        return this.createImageElement(src, options);
    }
  }

  /**
   * Create image element
   */
  createImageElement(src, options) {
    const img = document.createElement('img');
    img.src = src;
    img.alt = options.alt || 'Media content';
    img.loading = 'lazy';
    img.draggable = false;
    
    // Prevent context menu
    img.addEventListener('contextmenu', (e) => e.preventDefault());
    
    return img;
  }

  /**
   * Create video element
   */
  createVideoElement(src, options) {
    const video = document.createElement('video');
    video.src = src;
    video.controls = options.controls !== false;
    video.autoplay = options.autoplay || false;
    video.loop = options.loop || false;
    video.muted = options.muted !== false; // Default to muted
    video.playsInline = true;
    video.disablePictureInPicture = true;
    
    if (options.poster) {
      video.poster = options.poster;
    }
    
    // Add click handler for play/pause
    video.addEventListener('click', (e) => {
      e.stopPropagation();
      if (video.paused) {
        video.play().catch(console.error);
      } else {
        video.pause();
      }
    });
    
    // Prevent context menu
    video.addEventListener('contextmenu', (e) => e.preventDefault());
    
    return video;
  }

  /**
   * Create iframe element
   */
  createIframeElement(src, options) {
    const iframe = document.createElement('iframe');
    iframe.src = src;
    iframe.frameBorder = '0';
    iframe.allowFullscreen = true;
    iframe.loading = 'lazy';
    
    if (options.title) {
      iframe.title = options.title;
    }
    
    return iframe;
  }

  /**
   * Apply universal styling that works for all media types
   */
  applyMediaStyling(element, mediaData) {
    // Add universal classes
    element.classList.add('media-element');
    element.classList.add(`media-${mediaData.type.toLowerCase()}`);
    
    // Set initial state for smooth transitions
    element.style.opacity = '0';
    element.style.transition = 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    
    // Add data attributes for styling hooks
    element.setAttribute('data-media-type', mediaData.type);
    
    if (mediaData.options && mediaData.options.aspectRatio) {
      element.setAttribute('data-aspect-ratio', mediaData.options.aspectRatio);
    }
  }

  /**
   * Wait for media to fully load
   */
  waitForMediaLoad(element) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.warn('Media load timeout, proceeding anyway');
        resolve();
      }, 10000); // 10 second timeout

      const cleanup = () => {
        clearTimeout(timeout);
      };

      if (element.tagName === 'IMG') {
        if (element.complete && element.naturalWidth > 0) {
          cleanup();
          resolve();
        } else {
          element.onload = () => {
            cleanup();
            resolve();
          };
          element.onerror = (error) => {
            cleanup();
            reject(new Error(`Image failed to load: ${element.src}`));
          };
        }
      } else if (element.tagName === 'VIDEO') {
        if (element.readyState >= 2) { // HAVE_CURRENT_DATA
          cleanup();
          resolve();
        } else {
          element.onloadeddata = () => {
            cleanup();
            resolve();
          };
          element.onerror = (error) => {
            cleanup();
            reject(new Error(`Video failed to load: ${element.src}`));
          };
        }
      } else if (element.tagName === 'IFRAME') {
        element.onload = () => {
          cleanup();
          resolve();
        };
        element.onerror = (error) => {
          cleanup();
          reject(new Error(`Iframe failed to load: ${element.src}`));
        };
      } else {
        // For other elements, resolve immediately
        cleanup();
        resolve();
      }
    });
  }

  /**
   * Update container state and attributes
   */
  updateContainerState(mediaData) {
    this.container.setAttribute('data-media-type', mediaData.type);
    this.container.classList.add('has-media');
    
    // Add type-specific classes for styling
    this.container.className = this.container.className
      .replace(/media-type-\w+/g, '');
    this.container.classList.add(`media-type-${mediaData.type.toLowerCase()}`);
    
    // Update ARIA attributes for accessibility
    this.container.setAttribute('role', 'img');
    this.container.setAttribute('aria-label', 
      mediaData.options?.alt || `${mediaData.type} content`);
  }

  /**
   * Show content with smooth transition
   */
  showContent(element) {
    // Use requestAnimationFrame for smooth animation
    requestAnimationFrame(() => {
      element.style.opacity = '1';
    });
  }

  /**
   * Clear current content
   */
  clearContent() {
    if (this.mediaContent) {
      this.mediaContent.innerHTML = '';
    }

    this.container.classList.remove('has-media', 'loading', 'error');
    this.container.setAttribute('data-media-type', 'none');

    // Remove type-specific classes
    this.container.className = this.container.className
      .replace(/media-type-\w+/g, '');

    this.currentMedia = null;
  }

  /**
   * Show/hide loading state
   */
  setLoadingState(isLoading) {
    this.isLoading = isLoading;
    this.container.classList.toggle('loading', isLoading);

    if (isLoading) {
      // Add loading spinner
      const loader = document.createElement('div');
      loader.className = 'media-loader';
      loader.innerHTML = `
        <div class="spinner">
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">Loading...</div>
      `;
      this.mediaContent.appendChild(loader);
    } else {
      // Remove loader
      const loader = this.mediaContent.querySelector('.media-loader');
      if (loader) {
        loader.remove();
      }
    }
  }

  /**
   * Handle loading errors gracefully
   */
  handleLoadError(error, mediaData) {
    console.error('Media loading error:', error);

    this.setLoadingState(false);
    this.container.classList.add('error');

    // Show error state
    const errorElement = document.createElement('div');
    errorElement.className = 'media-error';
    errorElement.innerHTML = `
      <div class="error-icon">⚠️</div>
      <div class="error-message">
        <h3>Failed to load ${mediaData?.type || 'media'}</h3>
        <p>${error.message || 'Unknown error occurred'}</p>
      </div>
      <button class="retry-button" type="button">Try Again</button>
    `;

    // Add retry functionality
    const retryButton = errorElement.querySelector('.retry-button');
    retryButton.addEventListener('click', () => {
      this.loadMedia(mediaData);
    });

    this.mediaContent.appendChild(errorElement);
    this.dispatchEvent('media-error', { error, mediaData });
  }

  /**
   * Handle keyboard navigation
   */
  handleKeyboardNavigation(event) {
    switch (event.key) {
      case 'Escape':
        this.dispatchEvent('escape-pressed', { originalEvent: event });
        break;

      case 'ArrowLeft':
        event.preventDefault();
        this.dispatchEvent('navigate-previous', { originalEvent: event });
        break;

      case 'ArrowRight':
        event.preventDefault();
        this.dispatchEvent('navigate-next', { originalEvent: event });
        break;

      case ' ':
      case 'Enter':
        if (this.currentMedia && this.currentMedia.tagName === 'VIDEO') {
          event.preventDefault();
          if (this.currentMedia.paused) {
            this.currentMedia.play().catch(console.error);
          } else {
            this.currentMedia.pause();
          }
        }
        break;
    }
  }

  /**
   * Get current media dimensions
   */
  getMediaDimensions() {
    if (!this.currentMedia) return null;

    if (this.currentMedia.tagName === 'IMG') {
      return {
        width: this.currentMedia.naturalWidth,
        height: this.currentMedia.naturalHeight,
        aspectRatio: this.currentMedia.naturalWidth / this.currentMedia.naturalHeight
      };
    } else if (this.currentMedia.tagName === 'VIDEO') {
      return {
        width: this.currentMedia.videoWidth,
        height: this.currentMedia.videoHeight,
        aspectRatio: this.currentMedia.videoWidth / this.currentMedia.videoHeight
      };
    }

    return {
      width: this.currentMedia.offsetWidth,
      height: this.currentMedia.offsetHeight,
      aspectRatio: this.currentMedia.offsetWidth / this.currentMedia.offsetHeight
    };
  }

  /**
   * Update overlay content
   */
  updateOverlay(content) {
    if (this.overlay) {
      if (typeof content === 'string') {
        this.overlay.innerHTML = content;
      } else if (content instanceof HTMLElement) {
        this.overlay.innerHTML = '';
        this.overlay.appendChild(content);
      } else {
        this.overlay.innerHTML = '';
      }
    }
  }

  /**
   * Show/hide overlay
   */
  toggleOverlay(show = null) {
    if (!this.overlay) return;

    const shouldShow = show !== null ? show : !this.overlay.classList.contains('visible');
    this.overlay.classList.toggle('visible', shouldShow);

    this.dispatchEvent('overlay-toggled', { visible: shouldShow });
  }

  /**
   * Get current media element
   */
  getCurrentMedia() {
    return this.currentMedia;
  }

  /**
   * Check if container is currently loading
   */
  isLoadingMedia() {
    return this.isLoading;
  }

  /**
   * Dispatch custom events
   */
  dispatchEvent(eventName, detail = {}) {
    const event = new CustomEvent(`media-container-${eventName}`, {
      detail: {
        container: this,
        ...detail
      },
      bubbles: true,
      cancelable: true
    });
    this.container.dispatchEvent(event);
    return event;
  }

  /**
   * Destroy container and clean up
   */
  destroy() {
    // Clear content and cache
    this.clearContent();
    this.mediaCache.clear();

    // Remove event listeners
    this.container.removeEventListener('click', this.handleContainerClick);
    this.container.removeEventListener('keydown', this.handleKeyboardNavigation);

    // Reset container
    this.container.removeAttribute('data-media-type');
    this.container.removeAttribute('role');
    this.container.removeAttribute('aria-label');
    this.container.className = 'universal-media-container';

    // Clear references
    this.currentMedia = null;
    this.mediaContent = null;
    this.overlay = null;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = UniversalMediaContainer;
} else if (typeof window !== 'undefined') {
  window.UniversalMediaContainer = UniversalMediaContainer;
}
