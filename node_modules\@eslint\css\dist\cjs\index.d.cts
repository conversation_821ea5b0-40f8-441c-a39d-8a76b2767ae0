export type NoInvalidAtRulesMessageIds = "unknownAtRule" | "invalidPrelude" | "unknownDescriptor" | "invalidDescriptor" | "invalidExtraPrelude" | "missingPrelude";
export type NoInvalidAtRulesRuleDefinition = CSSRuleDefinition<{
    RuleOptions: [];
    MessageIds: NoInvalidAtRulesMessageIds;
}>;
export type CSSOkParseResult = OkParseResult<StyleSheetPlain> & {
    comments: Comment[];
    lexer: Lexer;
};
export type CSSParseResult = ParseResult<StyleSheetPlain>;
export type CSSLanguageOptions = {
    /**
     * Whether to be tolerant of recoverable parsing errors.
     */
    tolerant?: boolean;
    /**
     * Custom syntax to use for parsing.
     */
    customSyntax?: SyntaxConfig;
};
export type NoEmptyBlocksMessageIds = "emptyBlock";
export type NoEmptyBlocksRuleDefinition = CSSRuleDefinition<{
    RuleOptions: [];
    MessageIds: NoEmptyBlocksMessageIds;
}>;
export type NoDuplicateKeysMessageIds = "duplicateImport";
export type NoDuplicateImportsRuleDefinition = CSSRuleDefinition<{
    RuleOptions: [];
    MessageIds: NoDuplicateKeysMessageIds;
}>;
export type NoImportantMessageIds = "unexpectedImportant";
export type NoImportantRuleDefinition = CSSRuleDefinition<{
    RuleOptions: [];
    MessageIds: NoImportantMessageIds;
}>;
export type NoInvalidPropertiesMessageIds = "invalidPropertyValue" | "unknownProperty";
export type NoInvalidPropertiesRuleDefinition = CSSRuleDefinition<{
    RuleOptions: [];
    MessageIds: NoInvalidPropertiesMessageIds;
}>;
export type PreferLogicalPropertiesMessageIds = "notLogicalProperty" | "notLogicalValue" | "notLogicalUnit";
export type PreferLogicalPropertiesOptions = [{
    allowProperties?: string[];
    allowUnits?: string[];
}];
export type PreferLogicalPropertiesRuleDefinition = CSSRuleDefinition<{
    RuleOptions: PreferLogicalPropertiesOptions;
    MessageIds: PreferLogicalPropertiesMessageIds;
}>;
export type UseLayersMessageIds = "missingLayer" | "missingLayerName" | "missingImportLayer" | "layerNameMismatch";
export type UseLayersOptions = [{
    allowUnnamedLayers?: boolean;
    requireImportLayers?: boolean;
    layerNamePattern?: string;
}];
export type UseLayersRuleDefinition = CSSRuleDefinition<{
    RuleOptions: UseLayersOptions;
    MessageIds: UseLayersMessageIds;
}>;
export type UseBaselineMessageIds = "notBaselineProperty" | "notBaselinePropertyValue" | "notBaselineAtRule" | "notBaselineType" | "notBaselineMediaCondition" | "notBaselineSelector";
export type UseBaselineOptions = [{
    available?: "widely" | "newly" | number;
}];
export type UseBaselineRuleDefinition = CSSRuleDefinition<{
    RuleOptions: UseBaselineOptions;
    MessageIds: UseBaselineMessageIds;
}>;
/**
 * CSS Language Object
 * @implements {Language<{ LangOptions: CSSLanguageOptions; Code: CSSSourceCode; RootNode: StyleSheetPlain; Node: CssNodePlain}>}
 */
export class CSSLanguage implements Language {
    /**
     * The type of file to read.
     * @type {"text"}
     */
    fileType: "text";
    /**
     * The line number at which the parser starts counting.
     * @type {0|1}
     */
    lineStart: 0 | 1;
    /**
     * The column number at which the parser starts counting.
     * @type {0|1}
     */
    columnStart: 0 | 1;
    /**
     * The name of the key that holds the type of the node.
     * @type {string}
     */
    nodeTypeKey: string;
    /**
     * The visitor keys for the CSSTree AST.
     * @type {Record<string, string[]>}
     */
    visitorKeys: Record<string, string[]>;
    /**
     * The default language options.
     * @type {CSSLanguageOptions}
     */
    defaultLanguageOptions: CSSLanguageOptions;
    /**
     * Validates the language options.
     * @param {CSSLanguageOptions} languageOptions The language options to validate.
     * @throws {Error} When the language options are invalid.
     */
    validateLanguageOptions(languageOptions: CSSLanguageOptions): void;
    /**
     * Parses the given file into an AST.
     * @param {File} file The virtual file to parse.
     * @param {Object} [context] The parsing context.
     * @param {CSSLanguageOptions} [context.languageOptions] The language options to use for parsing.
     * @returns {CSSParseResult} The result of parsing.
     */
    parse(file: File, { languageOptions }?: {
        languageOptions?: CSSLanguageOptions;
    }): CSSParseResult;
    /**
     * Creates a new `CSSSourceCode` object from the given information.
     * @param {File} file The virtual file to create a `CSSSourceCode` object from.
     * @param {CSSOkParseResult} parseResult The result returned from `parse()`.
     * @returns {CSSSourceCode} The new `CSSSourceCode` object.
     */
    createSourceCode(file: File, parseResult: CSSOkParseResult): CSSSourceCode;
}
/**
 * CSS Source Code Object.
 * @extends {TextSourceCodeBase<{LangOptions: CSSLanguageOptions, RootNode: StyleSheetPlain, SyntaxElementWithLoc: CSSSyntaxElement, ConfigNode: Comment}>}
 */
export class CSSSourceCode extends TextSourceCodeBase<{
    LangOptions: CSSLanguageOptions;
    RootNode: StyleSheetPlain;
    SyntaxElementWithLoc: CSSSyntaxElement;
    ConfigNode: Comment;
}> {
    /**
     * Creates a new instance.
     * @param {Object} options The options for the instance.
     * @param {string} options.text The source code text.
     * @param {StyleSheetPlain} options.ast The root AST node.
     * @param {Array<Comment>} options.comments The comment nodes in the source code.
     * @param {Lexer} options.lexer The lexer used to parse the source code.
     */
    constructor({ text, ast, comments, lexer }: {
        text: string;
        ast: StyleSheetPlain;
        comments: Array<Comment>;
        lexer: Lexer;
    });
    /**
     * The comment node in the source code.
     * @type {Array<Comment>|undefined}
     */
    comments: Array<Comment> | undefined;
    /**
     * The lexer for this instance.
     * @type {Lexer}
     */
    lexer: Lexer;
    /**
     * Returns an array of all inline configuration nodes found in the
     * source code.
     * @returns {Array<Comment>} An array of all inline configuration nodes.
     */
    getInlineConfigNodes(): Array<Comment>;
    /**
     * Returns directives that enable or disable rules along with any problems
     * encountered while parsing the directives.
     * @returns {{problems:Array<FileProblem>,directives:Array<Directive>}} Information
     *      that ESLint needs to further process the directives.
     */
    getDisableDirectives(): {
        problems: Array<FileProblem>;
        directives: Array<Directive>;
    };
    /**
     * Returns inline rule configurations along with any problems
     * encountered while parsing the configurations.
     * @returns {{problems:Array<FileProblem>,configs:Array<{config:{rules:RulesConfig},loc:SourceLocation}>}} Information
     *      that ESLint needs to further process the rule configurations.
     */
    applyInlineConfig(): {
        problems: Array<FileProblem>;
        configs: Array<{
            config: {
                rules: RulesConfig;
            };
            loc: SourceLocation;
        }>;
    };
    /**
     * Traverse the source code and return the steps that were taken.
     * @returns {Iterable<CSSTraversalStep>} The steps that were taken while traversing the source code.
     */
    traverse(): Iterable<CSSTraversalStep>;
    #private;
}
declare namespace plugin {
    namespace meta {
        let name: string;
        let version: string;
    }
    namespace languages {
        let css: CSSLanguage;
    }
    let rules: {
        "no-empty-blocks": NoEmptyBlocksRuleDefinition;
        "no-duplicate-imports": NoDuplicateImportsRuleDefinition;
        "no-important": NoImportantRuleDefinition;
        "no-invalid-at-rules": CSSRuleDefinition<{
            RuleOptions: [];
            MessageIds: NoInvalidAtRulesMessageIds;
        }>;
        "no-invalid-properties": NoInvalidPropertiesRuleDefinition;
        "prefer-logical-properties": PreferLogicalPropertiesRuleDefinition;
        "use-layers": UseLayersRuleDefinition;
        "use-baseline": UseBaselineRuleDefinition;
    };
    namespace configs {
        namespace recommended {
            export let plugins: {};
            let rules_1: {
                readonly "css/no-empty-blocks": "error";
                readonly "css/no-duplicate-imports": "error";
                readonly "css/no-important": "error";
                readonly "css/no-invalid-at-rules": "error";
                readonly "css/no-invalid-properties": "error";
                readonly "css/use-baseline": "warn";
            };
            export { rules_1 as rules };
        }
    }
}
import type { CSSRuleDefinition } from "./types.cjs";
import type { StyleSheetPlain } from "@eslint/css-tree";
import type { OkParseResult } from "@eslint/core";
import type { Comment } from "@eslint/css-tree";
import type { Lexer } from "@eslint/css-tree";
import type { ParseResult } from "@eslint/core";
import type { SyntaxConfig } from "@eslint/css-tree";
import type { Language } from "@eslint/core";
import type { File } from "@eslint/core";
import type { CSSSyntaxElement } from "./types.cjs";
import { TextSourceCodeBase } from '@eslint/plugin-kit';
import type { FileProblem } from "@eslint/core";
import { Directive } from '@eslint/plugin-kit';
import type { RulesConfig } from "@eslint/core";
import type { SourceLocation } from "@eslint/core";
/**
 * A class to represent a step in the traversal process.
 */
declare class CSSTraversalStep extends VisitNodeStep {
    /**
     * Creates a new instance.
     * @param {Object} options The options for the step.
     * @param {CssNode} options.target The target of the step.
     * @param {1|2} options.phase The phase of the step.
     * @param {Array<any>} options.args The arguments of the step.
     */
    constructor({ target, phase, args }: {
        target: CssNode;
        phase: 1 | 2;
        args: Array<any>;
    });
    /**
     * The target of the step.
     * @type {CssNode}
     */
    target: CssNode;
}
import { VisitNodeStep } from '@eslint/plugin-kit';
import type { CssNode } from "@eslint/css-tree";
export { plugin as default };
