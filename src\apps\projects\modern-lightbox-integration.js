/*
 * modern-lightbox-integration.js - Integration Layer for Modern Lightbox
 * Connects the new modern lightbox system with existing project structure
 * Provides backward compatibility and migration path
 */

/**
 * Lightbox Integration Manager
 * Handles the transition from legacy lightbox to modern lightbox
 */
class LightboxIntegration {
  constructor() {
    this.modernLightbox = null;
    this.projectsData = [];
    this.isInitialized = false;
    this.legacyCompatMode = true; // Enable legacy API compatibility
  }

  /**
   * Initialize the integration system
   */
  async init(projectsData = []) {
    try {
      this.projectsData = projectsData;
      
      // Load required CSS files
      await this.loadStylesheets();
      
      // Initialize modern lightbox
      this.modernLightbox = new ModernLightbox({
        containerSelector: '#project-lightbox',
        closeOnBackdropClick: true,
        enableKeyboardNavigation: true,
        showNavigationControls: true
      });
      
      // Initialize with projects data
      this.modernLightbox.initWithData(this.projectsData);
      
      // Setup legacy API compatibility
      if (this.legacyCompatMode) {
        this.setupLegacyAPI();
      }
      
      // Setup project grid integration
      this.setupProjectGridIntegration();
      
      this.isInitialized = true;
      console.log('Modern lightbox integration initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize lightbox integration:', error);
      throw error;
    }
  }

  /**
   * Load required stylesheets
   */
  async loadStylesheets() {
    const stylesheets = [
      'universal-media-container.css',
      'modern-lightbox.css'
    ];

    const loadPromises = stylesheets.map(filename => {
      return new Promise((resolve, reject) => {
        // Check if already loaded
        if (document.querySelector(`link[href*="${filename}"]`)) {
          resolve();
          return;
        }

        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = filename;
        link.onload = resolve;
        link.onerror = () => reject(new Error(`Failed to load ${filename}`));
        document.head.appendChild(link);
      });
    });

    await Promise.all(loadPromises);
  }

  /**
   * Setup legacy API compatibility
   * Provides the same API as the old lightbox system
   */
  setupLegacyAPI() {
    // Create legacy Lightbox namespace for backward compatibility
    window.Lightbox = {
      /**
       * Legacy init method
       */
      init: (projectPostsData) => {
        if (projectPostsData) {
          this.projectsData = projectPostsData;
          this.modernLightbox.initWithData(projectPostsData);
        }
      },

      /**
       * Legacy open method
       */
      open: (projectIdx, imageIdx = 0) => {
        if (!this.isInitialized) {
          console.error('Lightbox not initialized. Call Lightbox.init() first.');
          return;
        }
        this.modernLightbox.open(projectIdx, imageIdx);
      },

      /**
       * Legacy close method
       */
      close: () => {
        if (this.modernLightbox) {
          this.modernLightbox.close();
        }
      },

      /**
       * Legacy toggle description method
       */
      toggleDescription: () => {
        if (this.modernLightbox) {
          this.modernLightbox.toggleInfo();
        }
      },

      /**
       * Legacy navigation methods
       */
      navigateNext: () => {
        if (this.modernLightbox) {
          this.modernLightbox.navigateNext();
        }
      },

      navigatePrevious: () => {
        if (this.modernLightbox) {
          this.modernLightbox.navigatePrevious();
        }
      }
    };
  }

  /**
   * Setup integration with existing project grid
   */
  setupProjectGridIntegration() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.attachProjectGridListeners();
      });
    } else {
      this.attachProjectGridListeners();
    }
  }

  /**
   * Attach event listeners to project grid items
   */
  attachProjectGridListeners() {
    // Find all project grid items
    const projectItems = document.querySelectorAll('[data-idx]');
    
    projectItems.forEach(item => {
      // Remove existing click listeners to avoid conflicts
      const newItem = item.cloneNode(true);
      item.parentNode.replaceChild(newItem, item);
      
      // Add new click listener
      newItem.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const projectIndex = parseInt(newItem.dataset.idx);
        if (!isNaN(projectIndex)) {
          this.openProject(projectIndex);
        }
      });
    });
  }

  /**
   * Open project in lightbox
   */
  openProject(projectIndex, mediaIndex = 0) {
    if (!this.isInitialized) {
      console.error('Lightbox integration not initialized');
      return;
    }

    try {
      this.modernLightbox.open(projectIndex, mediaIndex);
      
      // Notify parent window if in iframe
      this.notifyParent('lightbox-opened', {
        projectIndex,
        mediaIndex
      });
      
    } catch (error) {
      console.error('Failed to open project:', error);
    }
  }

  /**
   * Update projects data
   */
  updateProjectsData(newProjectsData) {
    this.projectsData = newProjectsData;
    if (this.modernLightbox) {
      this.modernLightbox.initWithData(newProjectsData);
    }
    
    // Re-attach grid listeners with new data
    this.attachProjectGridListeners();
  }

  /**
   * Get current lightbox state
   */
  getState() {
    return this.modernLightbox ? this.modernLightbox.getState() : null;
  }

  /**
   * Check if lightbox is currently open
   */
  isOpen() {
    const state = this.getState();
    return state ? state.isOpen : false;
  }

  /**
   * Notify parent window (for iframe communication)
   */
  notifyParent(type, data = {}) {
    if (window.parent && window.parent !== window) {
      try {
        window.parent.postMessage({
          type: `lightbox-${type}`,
          data
        }, '*');
      } catch (error) {
        console.warn('Failed to notify parent window:', error);
      }
    }
  }

  /**
   * Migrate from legacy lightbox
   * Helps transition existing implementations
   */
  async migrateFromLegacy() {
    try {
      // Check if legacy lightbox exists
      const legacyLightbox = document.getElementById('project-lightbox');
      if (legacyLightbox && legacyLightbox.innerHTML.trim()) {
        console.log('Legacy lightbox detected, preparing migration...');
        
        // Store any existing state
        const wasOpen = legacyLightbox.classList.contains('fade-in');
        
        // Clear legacy content
        legacyLightbox.innerHTML = '';
        legacyLightbox.className = 'modern-lightbox';
        
        console.log('Legacy lightbox migrated successfully');
        
        return { wasOpen };
      }
      
      return { wasOpen: false };
      
    } catch (error) {
      console.error('Migration from legacy lightbox failed:', error);
      throw error;
    }
  }

  /**
   * Destroy integration and clean up
   */
  destroy() {
    // Destroy modern lightbox
    if (this.modernLightbox) {
      this.modernLightbox.destroy();
      this.modernLightbox = null;
    }
    
    // Remove legacy API
    if (window.Lightbox) {
      delete window.Lightbox;
    }
    
    // Clear data
    this.projectsData = [];
    this.isInitialized = false;
    
    console.log('Lightbox integration destroyed');
  }
}

/**
 * Global integration instance
 */
let globalLightboxIntegration = null;

/**
 * Initialize the lightbox integration system
 * This is the main entry point for the new lightbox system
 */
async function initModernLightbox(projectsData = []) {
  try {
    if (globalLightboxIntegration) {
      globalLightboxIntegration.destroy();
    }
    
    globalLightboxIntegration = new LightboxIntegration();
    await globalLightboxIntegration.init(projectsData);
    
    return globalLightboxIntegration;
    
  } catch (error) {
    console.error('Failed to initialize modern lightbox:', error);
    throw error;
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    LightboxIntegration,
    initModernLightbox
  };
} else if (typeof window !== 'undefined') {
  window.LightboxIntegration = LightboxIntegration;
  window.initModernLightbox = initModernLightbox;
}
