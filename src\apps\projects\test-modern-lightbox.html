<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Lightbox Test</title>
    
    <!-- Load the new lightbox styles -->
    <link rel="stylesheet" href="universal-media-container.css">
    <link rel="stylesheet" href="modern-lightbox.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .test-item:hover {
            transform: translateY(-2px);
        }
        
        .test-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .test-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-controls button {
            margin-right: 10px;
            padding: 10px 20px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-controls button:hover {
            background: #005a9e;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Modern Lightbox System Test</h1>
        <p>This page tests the new Universal Media Container and Modern Lightbox implementation.</p>
        
        <div class="test-controls">
            <h3>Test Controls</h3>
            <button onclick="testImageLightbox()">Test Image Lightbox</button>
            <button onclick="testVideoLightbox()">Test Video Lightbox</button>
            <button onclick="testMultipleMedia()">Test Multiple Media</button>
            <button onclick="testErrorHandling()">Test Error Handling</button>
            <button onclick="runAllTests()">Run All Tests</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-grid">
            <div class="test-item" onclick="openTestProject(0)">
                <h3>Sample Image Project</h3>
                <p>Test project with a single image to verify basic lightbox functionality.</p>
            </div>
            
            <div class="test-item" onclick="openTestProject(1)">
                <h3>Sample Video Project</h3>
                <p>Test project with a video to verify video playback in the lightbox.</p>
            </div>
            
            <div class="test-item" onclick="openTestProject(2)">
                <h3>Multiple Media Project</h3>
                <p>Test project with multiple images and videos to verify navigation.</p>
            </div>
            
            <div class="test-item" onclick="openTestProject(3)">
                <h3>Error Test Project</h3>
                <p>Test project with invalid media URLs to verify error handling.</p>
            </div>
        </div>
    </div>

    <!-- Load the new lightbox scripts -->
    <script src="universal-media-container.js"></script>
    <script src="modern-lightbox.js"></script>
    <script src="modern-lightbox-integration.js"></script>
    
    <script>
        // Test data
        const testProjects = [
            {
                title: "Sample Image Project",
                description: "A test project with a beautiful landscape image.",
                type: "image",
                src: "https://picsum.photos/800/600?random=1",
                images: []
            },
            {
                title: "Sample Video Project", 
                description: "A test project with a sample video.",
                type: "video",
                src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                poster: "https://picsum.photos/800/600?random=2",
                images: []
            },
            {
                title: "Multiple Media Project",
                description: "A test project with multiple images and videos.",
                type: "image",
                src: "https://picsum.photos/800/600?random=3",
                images: [
                    {
                        type: "image",
                        src: "https://picsum.photos/800/600?random=4"
                    },
                    {
                        type: "image", 
                        src: "https://picsum.photos/800/600?random=5"
                    },
                    {
                        type: "video",
                        src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                        poster: "https://picsum.photos/800/600?random=6"
                    }
                ]
            },
            {
                title: "Error Test Project",
                description: "A test project with invalid URLs to test error handling.",
                type: "image",
                src: "https://invalid-url-that-does-not-exist.com/image.jpg",
                images: [
                    {
                        type: "image",
                        src: "https://another-invalid-url.com/image.jpg"
                    }
                ]
            }
        ];

        let lightboxIntegration = null;

        // Initialize the modern lightbox system
        async function initializeTest() {
            try {
                showStatus('Initializing modern lightbox system...', 'info');
                
                lightboxIntegration = await initModernLightbox(testProjects);
                
                showStatus('Modern lightbox system initialized successfully!', 'success');
                console.log('Test initialization complete');
                
            } catch (error) {
                showStatus(`Failed to initialize: ${error.message}`, 'error');
                console.error('Test initialization failed:', error);
            }
        }

        // Open a test project
        function openTestProject(index) {
            if (!lightboxIntegration) {
                showStatus('Lightbox not initialized yet', 'error');
                return;
            }
            
            try {
                lightboxIntegration.openProject(index);
                showStatus(`Opened project: ${testProjects[index].title}`, 'success');
            } catch (error) {
                showStatus(`Failed to open project: ${error.message}`, 'error');
            }
        }

        // Test functions
        function testImageLightbox() {
            showStatus('Testing image lightbox...', 'info');
            openTestProject(0);
        }

        function testVideoLightbox() {
            showStatus('Testing video lightbox...', 'info');
            openTestProject(1);
        }

        function testMultipleMedia() {
            showStatus('Testing multiple media navigation...', 'info');
            openTestProject(2);
        }

        function testErrorHandling() {
            showStatus('Testing error handling...', 'info');
            openTestProject(3);
        }

        async function runAllTests() {
            showStatus('Running all tests...', 'info');
            
            const tests = [
                () => testImageLightbox(),
                () => new Promise(resolve => setTimeout(() => { testVideoLightbox(); resolve(); }, 1000)),
                () => new Promise(resolve => setTimeout(() => { testMultipleMedia(); resolve(); }, 2000)),
                () => new Promise(resolve => setTimeout(() => { testErrorHandling(); resolve(); }, 3000))
            ];
            
            for (const test of tests) {
                await test();
            }
            
            showStatus('All tests completed!', 'success');
        }

        // Utility function to show status messages
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
    </script>
</body>
</html>
