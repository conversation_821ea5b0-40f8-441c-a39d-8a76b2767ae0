/*
 * universal-media-container.css - Styles for Universal Media Container
 * Flexible, responsive media display without aspect ratio constraints
 */

/* ===== Universal Media Container Base Styles ===== */
.universal-media-container {
  /* Container queries for true responsive design */
  container-type: inline-size;
  
  /* Flexible layout */
  display: grid;
  place-items: center;
  
  /* Adaptive sizing without fixed constraints */
  width: 100%;
  height: 100%;
  min-height: 300px;
  
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Base styling */
  position: relative;
  background: transparent;
  overflow: hidden;
  
  /* Focus styles for accessibility */
  outline: none;
}

.universal-media-container:focus-visible {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

/* ===== Media Content Area ===== */
.media-content {
  /* Flexible content area */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* ===== Universal Media Element Styles ===== */
.media-element {
  /* No aspect ratio constraints - preserve natural dimensions */
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  
  /* Smooth transitions */
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Base styling */
  display: block;
  border-radius: var(--media-border-radius, 12px);
  box-shadow: var(--media-shadow, 0 4px 20px rgba(0, 0, 0, 0.1));
}

/* ===== Media Type Specific Styles ===== */

/* Images */
.media-image {
  object-fit: contain; /* Preserve aspect ratio, no cropping */
  user-select: none;
  -webkit-user-drag: none;
}

/* Videos */
.media-video {
  object-fit: contain; /* Preserve aspect ratio, no cropping */
  cursor: pointer;
}

.media-video:hover {
  transform: scale(1.02);
}

/* Iframes */
.media-iframe {
  border: none;
  background: #fff;
}

/* ===== Container State Styles ===== */

/* Loading state */
.universal-media-container.loading {
  pointer-events: none;
}

.media-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #666;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.spinner {
  position: relative;
  width: 40px;
  height: 40px;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007acc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Error state */
.universal-media-container.error {
  background: #f8f8f8;
}

.media-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  color: #666;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.error-icon {
  font-size: 3rem;
  opacity: 0.7;
}

.error-message h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.error-message p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.retry-button {
  padding: 0.5rem 1rem;
  background: #007acc;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background: #005a9e;
}

.retry-button:active {
  transform: translateY(1px);
}

/* ===== Media Overlay ===== */
.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-overlay.visible {
  opacity: 1;
  pointer-events: auto;
}

/* ===== Responsive Behavior with Container Queries ===== */

/* Small containers */
@container (max-width: 400px) {
  .universal-media-container {
    --media-border-radius: 8px;
    --media-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 200px;
  }
  
  .media-loader {
    gap: 0.5rem;
  }
  
  .spinner {
    width: 30px;
    height: 30px;
  }
  
  .loading-text {
    font-size: 0.8rem;
  }
}

/* Medium containers */
@container (min-width: 401px) and (max-width: 800px) {
  .universal-media-container {
    --media-border-radius: 12px;
    --media-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
}

/* Large containers */
@container (min-width: 801px) {
  .universal-media-container {
    --media-border-radius: 16px;
    --media-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
  }
  
  .media-element:hover {
    transform: scale(1.01);
  }
}

/* ===== Media Type Specific Container Styles ===== */

/* Image containers */
.universal-media-container.media-type-image {
  background: #fafafa;
}

/* Video containers */
.universal-media-container.media-type-video {
  background: #000;
}

.universal-media-container.media-type-video .media-element {
  --media-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Iframe containers */
.universal-media-container.media-type-iframe {
  background: #fff;
}

/* ===== Accessibility Enhancements ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .universal-media-container {
    --media-shadow: 0 0 0 2px currentColor;
  }
  
  .media-element {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .universal-media-container,
  .media-element,
  .media-overlay,
  .spinner-ring {
    transition: none;
    animation: none;
  }
  
  .media-element:hover {
    transform: none;
  }
}

/* ===== Print Styles ===== */
@media print {
  .universal-media-container {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .media-loader,
  .media-error {
    display: none;
  }
  
  .media-element {
    max-width: 100% !important;
    height: auto !important;
  }
}
